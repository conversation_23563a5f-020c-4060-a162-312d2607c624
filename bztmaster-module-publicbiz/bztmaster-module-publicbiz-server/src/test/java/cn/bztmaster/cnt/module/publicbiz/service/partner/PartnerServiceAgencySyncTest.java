package cn.bztmaster.cnt.module.publicbiz.service.partner;

import cn.bztmaster.cnt.module.publicbiz.controller.admin.partner.vo.PartnerSaveReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.partner.vo.PartnerUpdateReqVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.partner.PartnerDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.AgencyDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.partner.PartnerMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.AgencyMapper;
import cn.bztmaster.cnt.module.publicbiz.service.partner.impl.PartnerServiceImpl;
import cn.bztmaster.cnt.module.publicbiz.service.employment.AgencyService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.junit.jupiter.api.Assertions.*;

/**
 * 合作伙伴服务机构同步功能测试
 */
public class PartnerServiceAgencySyncTest {

    @Mock
    private PartnerMapper partnerMapper;

    @Mock
    private AgencyMapper agencyMapper;

    @Mock
    private AgencyService agencyService;

    @InjectMocks
    private PartnerServiceImpl partnerService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testCreatePartner_家政机构_新增机构数据() {
        // 准备测试数据
        PartnerSaveReqVO reqVO = new PartnerSaveReqVO();
        reqVO.setName("测试家政机构");
        reqVO.setType("家政机构");
        reqVO.setCreditCode("91110000123456789X");
        reqVO.setStatus("合作中");
        reqVO.setOwner(1L);

        // 准备新创建的机构数据
        AgencyDO createdAgency = new AgencyDO();
        createdAgency.setId(100L);
        createdAgency.setAgencyName("测试家政机构");
        createdAgency.setCooperationStatus("pending"); // 初始状态
        createdAgency.setReviewStatus("pending"); // 初始状态

        // Mock 行为
        when(partnerMapper.selectByCreditCode(anyString())).thenReturn(null); // 统一社会信用代码不重复
        when(agencyMapper.selectByUnifiedSocialCreditCode(anyString())).thenReturn(null); // 机构不存在
        when(agencyService.createAgency(any())).thenReturn(100L); // 返回新创建的机构ID
        when(agencyMapper.selectById(100L)).thenReturn(createdAgency); // 返回创建的机构
        when(agencyMapper.updateById(any(AgencyDO.class))).thenReturn(1); // 更新机构状态
        when(partnerMapper.insert(any(PartnerDO.class))).thenReturn(1);

        // 执行测试
        Long partnerId = partnerService.createPartner(reqVO);

        // 验证结果
        verify(agencyService, times(1)).createAgency(any()); // 应该创建机构
        verify(agencyMapper, times(1)).updateById(any(AgencyDO.class)); // 应该更新机构状态
        verify(partnerMapper, times(1)).insert(any(PartnerDO.class)); // 应该插入合作伙伴
    }

    @Test
    void testCreatePartner_非家政机构_不同步机构数据() {
        // 准备测试数据
        PartnerSaveReqVO reqVO = new PartnerSaveReqVO();
        reqVO.setName("测试培训机构");
        reqVO.setType("培训机构");
        reqVO.setCreditCode("91110000123456789Y");
        reqVO.setStatus("合作中");
        reqVO.setOwner(1L);

        // Mock 行为
        when(partnerMapper.selectByCreditCode(anyString())).thenReturn(null);
        when(partnerMapper.insert(any(PartnerDO.class))).thenReturn(1);

        // 执行测试
        Long partnerId = partnerService.createPartner(reqVO);

        // 验证结果
        verify(agencyService, never()).createAgency(any()); // 不应该创建机构
        verify(partnerMapper, times(1)).insert(any(PartnerDO.class)); // 应该插入合作伙伴
    }

    @Test
    void testCreatePartner_家政机构_机构已存在() {
        // 准备测试数据
        PartnerSaveReqVO reqVO = new PartnerSaveReqVO();
        reqVO.setName("测试家政机构");
        reqVO.setType("家政机构");
        reqVO.setCreditCode("91110000123456789X");
        reqVO.setStatus("合作中");
        reqVO.setOwner(1L);

        // 准备已存在的机构数据
        AgencyDO existingAgency = new AgencyDO();
        existingAgency.setId(200L);
        existingAgency.setAgencyName("已存在的机构");
        existingAgency.setUnifiedSocialCreditCode("91110000123456789X");

        // Mock 行为
        when(partnerMapper.selectByCreditCode(anyString())).thenReturn(null);
        when(agencyMapper.selectByUnifiedSocialCreditCode(anyString())).thenReturn(existingAgency);
        when(partnerMapper.insert(any(PartnerDO.class))).thenReturn(1);

        // 执行测试
        Long partnerId = partnerService.createPartner(reqVO);

        // 验证结果
        verify(agencyService, never()).createAgency(any()); // 不应该创建新机构
        verify(partnerMapper, times(1)).insert(any(PartnerDO.class)); // 应该插入合作伙伴
    }

    @Test
    void testCreatePartner_统一社会信用代码重复_抛出异常() {
        // 准备测试数据
        PartnerSaveReqVO reqVO = new PartnerSaveReqVO();
        reqVO.setName("测试家政机构");
        reqVO.setType("家政机构");
        reqVO.setCreditCode("91110000123456789X");
        reqVO.setStatus("合作中");
        reqVO.setOwner(1L);

        // 准备已存在的合作伙伴数据
        PartnerDO existingPartner = new PartnerDO();
        existingPartner.setId(1L);
        existingPartner.setCreditCode("91110000123456789X");

        // Mock 行为
        when(partnerMapper.selectByCreditCode(anyString())).thenReturn(existingPartner);

        // 执行测试并验证异常
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            partnerService.createPartner(reqVO);
        });

        assertEquals("统一社会信用代码已存在，不能重复", exception.getMessage());
        verify(partnerMapper, never()).insert(any(PartnerDO.class)); // 不应该插入
    }

    @Test
    void testCreatePartner_家政机构_统一社会信用代码为空_抛出异常() {
        // 准备测试数据
        PartnerSaveReqVO reqVO = new PartnerSaveReqVO();
        reqVO.setName("测试家政机构");
        reqVO.setType("家政机构");
        reqVO.setCreditCode(""); // 空的统一社会信用代码
        reqVO.setStatus("合作中");
        reqVO.setOwner(1L);

        // Mock 行为
        when(partnerMapper.selectByCreditCode(anyString())).thenReturn(null);

        // 执行测试并验证异常
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            partnerService.createPartner(reqVO);
        });

        assertEquals("家政机构的统一社会信用代码不能为空", exception.getMessage());
    }

    @Test
    void testCreatePartner_家政机构_验证机构状态设置() {
        // 准备测试数据
        PartnerSaveReqVO reqVO = new PartnerSaveReqVO();
        reqVO.setName("测试家政机构");
        reqVO.setType("家政机构");
        reqVO.setCreditCode("91110000123456789X");
        reqVO.setStatus("合作中");
        reqVO.setOwner(1L);

        // 准备新创建的机构数据
        AgencyDO createdAgency = new AgencyDO();
        createdAgency.setId(100L);
        createdAgency.setAgencyName("测试家政机构");
        createdAgency.setCooperationStatus("pending"); // 初始状态
        createdAgency.setReviewStatus("pending"); // 初始状态

        // Mock 行为
        when(partnerMapper.selectByCreditCode(anyString())).thenReturn(null);
        when(agencyMapper.selectByUnifiedSocialCreditCode(anyString())).thenReturn(null);
        when(agencyService.createAgency(any())).thenReturn(100L);
        when(agencyMapper.selectById(100L)).thenReturn(createdAgency);
        when(agencyMapper.updateById(any(AgencyDO.class))).thenReturn(1);
        when(partnerMapper.insert(any(PartnerDO.class))).thenReturn(1);

        // 执行测试
        partnerService.createPartner(reqVO);

        // 验证机构状态被正确设置
        verify(agencyMapper, times(1)).updateById(argThat(agency ->
            "cooperating".equals(agency.getCooperationStatus()) &&
            "approved".equals(agency.getReviewStatus())
        ));
    }
}
