package cn.bztmaster.cnt.module.publicbiz.service.employment;

import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.ServicePackageSaveReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.ServicePackageUpdateReqVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.ServicePackageDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.AgencyDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.ServicePackageMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.AgencyMapper;
import cn.bztmaster.cnt.module.publicbiz.service.employment.impl.ServicePackageServiceImpl;
import cn.bztmaster.cnt.framework.security.core.util.SecurityFrameworkUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.junit.jupiter.api.Assertions.*;

/**
 * 服务套餐机构设置功能测试
 */
public class ServicePackageAgencySettingTest {

    @Mock
    private ServicePackageMapper servicePackageMapper;

    @Mock
    private AgencyMapper agencyMapper;

    @InjectMocks
    private ServicePackageServiceImpl servicePackageService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testCreateServicePackage_前端未传递机构ID_使用当前用户机构ID() {
        // 准备测试数据
        ServicePackageSaveReqVO reqVO = new ServicePackageSaveReqVO();
        reqVO.setName("测试套餐");
        reqVO.setCategory("日常保洁");
        reqVO.setPrice(new BigDecimal("100.00"));
        reqVO.setUnit("次");
        reqVO.setPackageType("count-card");
        reqVO.setAgencyId(null); // 前端未传递机构ID

        // 准备机构数据
        AgencyDO agency = new AgencyDO();
        agency.setId(200L);
        agency.setAgencyName("测试机构");
        agency.setDeleted(false);

        // Mock 静态方法
        try (MockedStatic<SecurityFrameworkUtils> mockedStatic = mockStatic(SecurityFrameworkUtils.class)) {
            mockedStatic.when(SecurityFrameworkUtils::getLoginUserAgencyId).thenReturn(200L);
            mockedStatic.when(SecurityFrameworkUtils::getLoginUserNickname).thenReturn("测试用户");
            mockedStatic.when(SecurityFrameworkUtils::getLoginUser).thenReturn(null);

            // Mock 其他行为
            when(agencyMapper.selectById(200L)).thenReturn(agency);
            when(servicePackageMapper.insert(any(ServicePackageDO.class))).thenReturn(1);

            // 执行测试
            Long packageId = servicePackageService.createServicePackage(reqVO);

            // 验证结果
            verify(agencyMapper, times(1)).selectById(200L); // 应该查询机构信息
            verify(servicePackageMapper, times(1)).insert(argThat(servicePackage -> 
                servicePackage.getAgencyId().equals(200L) && 
                "测试机构".equals(servicePackage.getAgencyName())
            ));
        }
    }

    @Test
    void testCreateServicePackage_前端传递机构ID_使用前端传递的机构ID() {
        // 准备测试数据
        ServicePackageSaveReqVO reqVO = new ServicePackageSaveReqVO();
        reqVO.setName("测试套餐");
        reqVO.setCategory("日常保洁");
        reqVO.setPrice(new BigDecimal("100.00"));
        reqVO.setUnit("次");
        reqVO.setPackageType("count-card");
        reqVO.setAgencyId(300L); // 前端传递了机构ID

        // 准备机构数据
        AgencyDO agency = new AgencyDO();
        agency.setId(300L);
        agency.setAgencyName("前端指定机构");
        agency.setDeleted(false);

        // Mock 静态方法
        try (MockedStatic<SecurityFrameworkUtils> mockedStatic = mockStatic(SecurityFrameworkUtils.class)) {
            mockedStatic.when(SecurityFrameworkUtils::getLoginUserAgencyId).thenReturn(200L);
            mockedStatic.when(SecurityFrameworkUtils::getLoginUserNickname).thenReturn("测试用户");
            mockedStatic.when(SecurityFrameworkUtils::getLoginUser).thenReturn(null);

            // Mock 其他行为
            when(agencyMapper.selectById(300L)).thenReturn(agency);
            when(servicePackageMapper.insert(any(ServicePackageDO.class))).thenReturn(1);

            // 执行测试
            Long packageId = servicePackageService.createServicePackage(reqVO);

            // 验证结果
            verify(agencyMapper, times(1)).selectById(300L); // 应该查询前端指定的机构
            verify(servicePackageMapper, times(1)).insert(argThat(servicePackage -> 
                servicePackage.getAgencyId().equals(300L) && 
                "前端指定机构".equals(servicePackage.getAgencyName())
            ));
        }
    }

    @Test
    void testCreateServicePackage_机构不存在_设置机构名称为null() {
        // 准备测试数据
        ServicePackageSaveReqVO reqVO = new ServicePackageSaveReqVO();
        reqVO.setName("测试套餐");
        reqVO.setCategory("日常保洁");
        reqVO.setPrice(new BigDecimal("100.00"));
        reqVO.setUnit("次");
        reqVO.setPackageType("count-card");
        reqVO.setAgencyId(400L);

        // Mock 静态方法
        try (MockedStatic<SecurityFrameworkUtils> mockedStatic = mockStatic(SecurityFrameworkUtils.class)) {
            mockedStatic.when(SecurityFrameworkUtils::getLoginUserNickname).thenReturn("测试用户");
            mockedStatic.when(SecurityFrameworkUtils::getLoginUser).thenReturn(null);

            // Mock 机构不存在
            when(agencyMapper.selectById(400L)).thenReturn(null);
            when(servicePackageMapper.insert(any(ServicePackageDO.class))).thenReturn(1);

            // 执行测试
            Long packageId = servicePackageService.createServicePackage(reqVO);

            // 验证结果
            verify(servicePackageMapper, times(1)).insert(argThat(servicePackage -> 
                servicePackage.getAgencyId().equals(400L) && 
                servicePackage.getAgencyName() == null
            ));
        }
    }

    @Test
    void testCreateServicePackage_机构已删除_设置机构名称为null() {
        // 准备测试数据
        ServicePackageSaveReqVO reqVO = new ServicePackageSaveReqVO();
        reqVO.setName("测试套餐");
        reqVO.setCategory("日常保洁");
        reqVO.setPrice(new BigDecimal("100.00"));
        reqVO.setUnit("次");
        reqVO.setPackageType("count-card");
        reqVO.setAgencyId(500L);

        // 准备已删除的机构数据
        AgencyDO deletedAgency = new AgencyDO();
        deletedAgency.setId(500L);
        deletedAgency.setAgencyName("已删除机构");
        deletedAgency.setDeleted(true);

        // Mock 静态方法
        try (MockedStatic<SecurityFrameworkUtils> mockedStatic = mockStatic(SecurityFrameworkUtils.class)) {
            mockedStatic.when(SecurityFrameworkUtils::getLoginUserNickname).thenReturn("测试用户");
            mockedStatic.when(SecurityFrameworkUtils::getLoginUser).thenReturn(null);

            // Mock 行为
            when(agencyMapper.selectById(500L)).thenReturn(deletedAgency);
            when(servicePackageMapper.insert(any(ServicePackageDO.class))).thenReturn(1);

            // 执行测试
            Long packageId = servicePackageService.createServicePackage(reqVO);

            // 验证结果
            verify(servicePackageMapper, times(1)).insert(argThat(servicePackage -> 
                servicePackage.getAgencyId().equals(500L) && 
                servicePackage.getAgencyName() == null
            ));
        }
    }

    @Test
    void testUpdateServicePackage_机构ID变更_同步机构名称() {
        // 准备测试数据
        ServicePackageUpdateReqVO updateReqVO = new ServicePackageUpdateReqVO();
        updateReqVO.setId(1L);
        updateReqVO.setName("更新后套餐");
        updateReqVO.setCategory("深度保洁");
        updateReqVO.setPrice(new BigDecimal("200.00"));
        updateReqVO.setUnit("次");
        updateReqVO.setPackageType("count-card");
        updateReqVO.setAgencyId(600L); // 新的机构ID

        // 准备旧套餐数据
        ServicePackageDO oldPackage = new ServicePackageDO();
        oldPackage.setId(1L);
        oldPackage.setAgencyId(500L); // 旧的机构ID

        // 准备新机构数据
        AgencyDO newAgency = new AgencyDO();
        newAgency.setId(600L);
        newAgency.setAgencyName("新机构");
        newAgency.setDeleted(false);

        // Mock 行为
        when(servicePackageMapper.selectById(1L)).thenReturn(oldPackage);
        when(agencyMapper.selectById(600L)).thenReturn(newAgency);
        when(servicePackageMapper.updateById(any(ServicePackageDO.class))).thenReturn(1);

        // 执行测试
        servicePackageService.updateServicePackage(updateReqVO);

        // 验证结果
        verify(agencyMapper, times(1)).selectById(600L); // 应该查询新机构信息
        verify(servicePackageMapper, times(1)).updateById(argThat(servicePackage -> 
            servicePackage.getAgencyId().equals(600L) && 
            "新机构".equals(servicePackage.getAgencyName())
        ));
    }

    @Test
    void testUpdateServicePackage_机构ID未变更_不查询机构信息() {
        // 准备测试数据
        ServicePackageUpdateReqVO updateReqVO = new ServicePackageUpdateReqVO();
        updateReqVO.setId(1L);
        updateReqVO.setName("更新后套餐");
        updateReqVO.setCategory("深度保洁");
        updateReqVO.setPrice(new BigDecimal("200.00"));
        updateReqVO.setUnit("次");
        updateReqVO.setPackageType("count-card");
        updateReqVO.setAgencyId(500L); // 相同的机构ID

        // 准备旧套餐数据
        ServicePackageDO oldPackage = new ServicePackageDO();
        oldPackage.setId(1L);
        oldPackage.setAgencyId(500L); // 相同的机构ID

        // Mock 行为
        when(servicePackageMapper.selectById(1L)).thenReturn(oldPackage);
        when(servicePackageMapper.updateById(any(ServicePackageDO.class))).thenReturn(1);

        // 执行测试
        servicePackageService.updateServicePackage(updateReqVO);

        // 验证结果
        verify(agencyMapper, never()).selectById(anyLong()); // 不应该查询机构信息
        verify(servicePackageMapper, times(1)).updateById(any(ServicePackageDO.class));
    }
}
