package cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 服务套餐统计报表响应 VO
 * 
 * <AUTHOR>
 */
@Data
@Schema(description = "就业服务-服务套餐统计报表 Response VO")
public class ServicePackageStatisticsRespVO {

    @Schema(description = "已上架数量，status='active'的套餐数量", example = "25")
    private Long activeCount;

    @Schema(description = "待上架数量，status='pending'的套餐数量", example = "8")
    private Long pendingCount;

    @Schema(description = "回收站数量，status='deleted'的套餐数量", example = "3")
    private Long deletedCount;

    @Schema(description = "总数量，所有状态套餐的总数（不包括物理删除的数据）", example = "36")
    private Long totalCount;

    @Schema(description = "统计范围的合作伙伴ID，如果为null则表示当前用户权限范围内的全部数据", example = "1001")
    private Long partnerId;

    @Schema(description = "统计范围的合作伙伴名称", example = "XX合作伙伴")
    private String partnerName;

    @Schema(description = "统计时间，数据生成的时间戳", example = "2024-01-15T10:30:00")
    private LocalDateTime statisticsTime;

    /**
     * 默认构造方法
     */
    public ServicePackageStatisticsRespVO() {
        this.statisticsTime = LocalDateTime.now();
    }

    /**
     * 构造方法
     * 
     * @param activeCount 已上架数量
     * @param pendingCount 待上架数量
     * @param deletedCount 回收站数量
     */
    public ServicePackageStatisticsRespVO(Long activeCount, Long pendingCount, Long deletedCount) {
        this.activeCount = activeCount != null ? activeCount : 0L;
        this.pendingCount = pendingCount != null ? pendingCount : 0L;
        this.deletedCount = deletedCount != null ? deletedCount : 0L;
        this.calculateTotalCount();
        this.statisticsTime = LocalDateTime.now();
    }

    /**
     * 构造方法
     * 
     * @param activeCount 已上架数量
     * @param pendingCount 待上架数量
     * @param deletedCount 回收站数量
     * @param partnerId 合作伙伴ID
     * @param partnerName 合作伙伴名称
     */
    public ServicePackageStatisticsRespVO(Long activeCount, Long pendingCount, Long deletedCount, 
                                         Long partnerId, String partnerName) {
        this.activeCount = activeCount != null ? activeCount : 0L;
        this.pendingCount = pendingCount != null ? pendingCount : 0L;
        this.deletedCount = deletedCount != null ? deletedCount : 0L;
        this.partnerId = partnerId;
        this.partnerName = partnerName;
        this.calculateTotalCount();
        this.statisticsTime = LocalDateTime.now();
    }

    /**
     * 计算并设置总数量
     */
    public void calculateTotalCount() {
        this.totalCount = (this.activeCount != null ? this.activeCount : 0L) +
                         (this.pendingCount != null ? this.pendingCount : 0L) +
                         (this.deletedCount != null ? this.deletedCount : 0L);
    }

    /**
     * 设置已上架数量
     * 
     * @param activeCount 已上架数量
     */
    public void setActiveCount(Long activeCount) {
        this.activeCount = activeCount != null ? activeCount : 0L;
        this.calculateTotalCount();
    }

    /**
     * 设置待上架数量
     * 
     * @param pendingCount 待上架数量
     */
    public void setPendingCount(Long pendingCount) {
        this.pendingCount = pendingCount != null ? pendingCount : 0L;
        this.calculateTotalCount();
    }

    /**
     * 设置回收站数量
     * 
     * @param deletedCount 回收站数量
     */
    public void setDeletedCount(Long deletedCount) {
        this.deletedCount = deletedCount != null ? deletedCount : 0L;
        this.calculateTotalCount();
    }
}
