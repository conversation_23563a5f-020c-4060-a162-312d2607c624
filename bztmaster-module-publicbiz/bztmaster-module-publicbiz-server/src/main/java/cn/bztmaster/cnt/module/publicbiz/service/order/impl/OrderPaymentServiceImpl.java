package cn.bztmaster.cnt.module.publicbiz.service.order.impl;

import cn.bztmaster.cnt.framework.common.exception.ServiceException;
import cn.bztmaster.cnt.framework.common.util.date.LocalDateTimeUtils;
import cn.bztmaster.cnt.module.publicbiz.api.order.dto.OrderCollectionReqDTO;
import cn.bztmaster.cnt.module.publicbiz.api.order.dto.OrderCollectionRespDTO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.order.PublicbizOrderPaymentDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.order.PublicbizOrderPaymentMapper;
import cn.bztmaster.cnt.module.publicbiz.enums.PaymentMethodEnum;
import cn.bztmaster.cnt.module.publicbiz.service.order.OrderPaymentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

import static cn.bztmaster.cnt.module.publicbiz.enums.ErrorCodeConstants.*;

/**
 * 订单支付记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class OrderPaymentServiceImpl implements OrderPaymentService {

    @Resource
    private PublicbizOrderPaymentMapper orderPaymentMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createPaymentRecord(OrderCollectionReqDTO reqDTO) {
        // 验证收款方式
        PaymentMethodEnum paymentMethod = PaymentMethodEnum.getByCode(reqDTO.getCollectionMethod());
        if (paymentMethod == null) {
            throw new ServiceException(PAYMENT_METHOD_NOT_EXISTS);
        }

        // 创建支付记录
        PublicbizOrderPaymentDO paymentDO = PublicbizOrderPaymentDO.builder()
                .orderId(reqDTO.getOrderId())
                .orderNo(reqDTO.getOrderNo())
                .paymentNo(generatePaymentNo())
                .paymentType(reqDTO.getCollectionMethod())
                .paymentAmount(reqDTO.getCollectionAmount())
                .paymentStatus("success")
                .paymentTime(LocalDateTime.now())
                .operatorId(getCurrentUserId())
                .operatorName(reqDTO.getOperatorName())
                .paymentRemark(reqDTO.getCollectionRemark())
                .transactionId(reqDTO.getTransactionId())
                .build();

        // 保存到数据库
        orderPaymentMapper.insert(paymentDO);
        log.info("创建支付记录成功，支付记录ID：{}，订单号：{}", paymentDO.getId(), paymentDO.getOrderNo());

        return paymentDO.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long updatePaymentRecord(OrderCollectionReqDTO reqDTO) {
        // 查询现有的支付记录
        List<PublicbizOrderPaymentDO> existingPayments = getPaymentRecordsByOrderId(reqDTO.getOrderId());
        if (existingPayments.isEmpty()) {
            throw new ServiceException(PAYMENT_RECORD_NOT_EXISTS);
        }

        // 更新第一条支付记录（假设一个订单只有一条支付记录）
        PublicbizOrderPaymentDO existingPayment = existingPayments.get(0);

        // 验证收款方式
        PaymentMethodEnum paymentMethod = PaymentMethodEnum.getByCode(reqDTO.getCollectionMethod());
        if (paymentMethod == null) {
            throw new ServiceException(PAYMENT_METHOD_NOT_EXISTS);
        }

        // 更新支付记录
        existingPayment.setPaymentType(reqDTO.getCollectionMethod());
        existingPayment.setPaymentAmount(reqDTO.getCollectionAmount());
        existingPayment.setPaymentTime(LocalDateTime.now());
        existingPayment.setOperatorId(getCurrentUserId());
        existingPayment.setOperatorName(reqDTO.getOperatorName());
        existingPayment.setPaymentRemark(reqDTO.getCollectionRemark());
        existingPayment.setTransactionId(reqDTO.getTransactionId());

        // 更新到数据库
        orderPaymentMapper.updateById(existingPayment);
        log.info("更新支付记录成功，支付记录ID：{}，订单号：{}", existingPayment.getId(), existingPayment.getOrderNo());

        return existingPayment.getId();
    }

    @Override
    public List<PublicbizOrderPaymentDO> getPaymentRecordsByOrderId(Long orderId) {
        return orderPaymentMapper.selectList(PublicbizOrderPaymentDO::getOrderId, orderId);
    }

    @Override
    public List<PublicbizOrderPaymentDO> getPaymentRecordsByOrderNo(String orderNo) {
        return orderPaymentMapper.selectList(PublicbizOrderPaymentDO::getOrderNo, orderNo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OrderCollectionRespDTO confirmCollection(OrderCollectionReqDTO reqDTO) {
        // 创建支付记录
        Long paymentId = createPaymentRecord(reqDTO);

        // 查询创建的支付记录
        PublicbizOrderPaymentDO paymentDO = orderPaymentMapper.selectById(paymentId);

        // 转换为响应DTO
        return convertToRespDTO(paymentDO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OrderCollectionRespDTO updateCollection(OrderCollectionReqDTO reqDTO) {
        // 更新支付记录
        Long paymentId = updatePaymentRecord(reqDTO);

        // 查询更新后的支付记录
        PublicbizOrderPaymentDO paymentDO = orderPaymentMapper.selectById(paymentId);

        // 转换为响应DTO
        return convertToRespDTO(paymentDO);
    }

    @Override
    public String generatePaymentNo() {
        // 生成支付单号：PAY + 年月日 + 6位序号
        String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String sequence = String.format("%06d", System.currentTimeMillis() % 1000000);
        return "PAY" + dateStr + sequence;
    }

    /**
     * 将DO转换为响应DTO
     *
     * @param paymentDO 支付记录DO
     * @return 响应DTO
     */
    private OrderCollectionRespDTO convertToRespDTO(PublicbizOrderPaymentDO paymentDO) {
        OrderCollectionRespDTO respDTO = new OrderCollectionRespDTO();
        respDTO.setPaymentId(paymentDO.getId());
        respDTO.setOrderId(paymentDO.getOrderId());
        respDTO.setOrderNo(paymentDO.getOrderNo());
        respDTO.setPaymentNo(paymentDO.getPaymentNo());
        respDTO.setPaymentType(paymentDO.getPaymentType());
        respDTO.setPaymentAmount(paymentDO.getPaymentAmount());
        respDTO.setPaymentStatus(paymentDO.getPaymentStatus());
        respDTO.setPaymentTime(paymentDO.getPaymentTime());
        respDTO.setOperatorId(paymentDO.getOperatorId());
        respDTO.setOperatorName(paymentDO.getOperatorName());
        respDTO.setPaymentRemark(paymentDO.getPaymentRemark());
        respDTO.setTransactionId(paymentDO.getTransactionId());
        respDTO.setCreateTime(paymentDO.getCreateTime());
        return respDTO;
    }

    /**
     * 获取当前用户ID
     * TODO: 实现获取当前登录用户ID的逻辑
     *
     * @return 当前用户ID
     */
    private Long getCurrentUserId() {
        // TODO: 从安全上下文获取当前用户ID
        return 1L;
    }
}
