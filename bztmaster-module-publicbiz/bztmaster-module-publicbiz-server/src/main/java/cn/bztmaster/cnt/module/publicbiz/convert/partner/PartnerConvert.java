package cn.bztmaster.cnt.module.publicbiz.convert.partner;

import cn.bztmaster.cnt.module.publicbiz.controller.admin.partner.vo.*;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.partner.PartnerDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import java.sql.Date;
import java.util.List;

/**
 * 合作伙伴对象转换器
 */
@Mapper(componentModel = "spring")
public interface PartnerConvert {

    @Mapping(target = "foundationDate", source = "foundationDate", qualifiedByName = "stringToSqlDate")
    @Mapping(target = "contractStart", source = "contractStart", qualifiedByName = "stringToSqlDate")
    @Mapping(target = "contractEnd", source = "contractEnd", qualifiedByName = "stringToSqlDate")
    @Mapping(target = "renewDate", source = "renewDate", qualifiedByName = "stringToSqlDate")
    @Mapping(target = "ownerName", source = "ownerName")
    @Mapping(target = "agencyId", source = "agencyId")
    @Mapping(target = "agencyName", source = "agencyName")
    PartnerDO convert(PartnerSaveReqVO bean);

    @Mapping(target = "foundationDate", source = "foundationDate", qualifiedByName = "stringToSqlDate")
    @Mapping(target = "contractStart", source = "contractStart", qualifiedByName = "stringToSqlDate")
    @Mapping(target = "contractEnd", source = "contractEnd", qualifiedByName = "stringToSqlDate")
    @Mapping(target = "renewDate", source = "renewDate", qualifiedByName = "stringToSqlDate")
    @Mapping(target = "ownerName", source = "ownerName")
    @Mapping(target = "agencyId", source = "agencyId")
    @Mapping(target = "agencyName", source = "agencyName")
    PartnerDO convert(PartnerUpdateReqVO bean);

    @Mapping(target = "foundationDate", source = "foundationDate", qualifiedByName = "dateToString")
    @Mapping(target = "contractStart", source = "contractStart", qualifiedByName = "dateToString")
    @Mapping(target = "contractEnd", source = "contractEnd", qualifiedByName = "dateToString")
    @Mapping(target = "renewDate", source = "renewDate", qualifiedByName = "dateToString")
    @Mapping(target = "createTime", source = "createTime", qualifiedByName = "dateTimeToString")
    @Mapping(target = "updateTime", source = "updateTime", qualifiedByName = "dateTimeToString")
    @Mapping(target = "ownerName", source = "ownerName")
    @Mapping(target = "agencyId", source = "agencyId")
    @Mapping(target = "agencyName", source = "agencyName")
    PartnerRespVO convert(PartnerDO bean);

    @Mapping(target = "foundationDate", source = "foundationDate", qualifiedByName = "dateToString")
    @Mapping(target = "contractStart", source = "contractStart", qualifiedByName = "dateToString")
    @Mapping(target = "contractEnd", source = "contractEnd", qualifiedByName = "dateToString")
    @Mapping(target = "renewDate", source = "renewDate", qualifiedByName = "dateToString")
    @Mapping(target = "ownerName", source = "ownerName")
    @Mapping(target = "agencyId", source = "agencyId")
    @Mapping(target = "agencyName", source = "agencyName")
    PartnerUpdateReqVO convertToUpdateReqVO(PartnerDO bean);

    List<PartnerRespVO> convertList(List<PartnerDO> list);

    @Named("stringToSqlDate")
    default java.sql.Date stringToSqlDate(String value) {
        if (value == null || value.isEmpty())
            return null;
        try {
            // 支持yyyy-MM-dd和yyyy-MM-ddTHH:mm:ss.SSSZ
            if (value.contains("T")) {
                return java.sql.Date.valueOf(value.substring(0, 10));
            } else {
                return java.sql.Date.valueOf(value);
            }
        } catch (Exception e) {
            return null;
        }
    }

    @Named("dateToString")
    default String dateToString(java.util.Date date) {
        if (date == null)
            return null;
        return new java.text.SimpleDateFormat("yyyy-MM-dd").format(date);
    }

    @Named("dateTimeToString")
    default String dateTimeToString(java.util.Date date) {
        if (date == null)
            return null;
        return new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(date);
    }

    // 其它VO/DO/DTO转换方法可补充
}