package cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employer;

import cn.bztmaster.cnt.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;

/**
 * 阿姨评价 DO
 *
 * <AUTHOR>
 */
@TableName("publicbiz_aunt_review")
@KeySequence("publicbiz_aunt_review_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AuntReviewDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 订单ID
     */
    private Long orderId;
    /**
     * 阿姨OneID
     */
    private String auntId;
    /**
     * 评价人用户ID
     */
    private Long reviewerId;
    /**
     * 评价人姓名
     */
    private String reviewerName;
    /**
     * 评价人头像URL
     */
    private String reviewerAvatar;
    /**
     * 服务态度评分：0.0-5.0
     */
    private BigDecimal attitudeRating;
    /**
     * 技术专业性评分：0.0-5.0
     */
    private BigDecimal professionalRating;
    /**
     * 责任心评分：0.0-5.0
     */
    private BigDecimal responsibilityRating;
    /**
     * 总评分：自动计算（三个维度评分的平均值）
     */
    private BigDecimal rating;
    /**
     * 评价标签（JSON格式）
     */
    private String reviewTags;
    /**
     * 评价内容
     */
    private String reviewContent;
    /**
     * 评价图片URL列表（JSON格式）
     */
    private String reviewImages;
    /**
     * 评价类型
     */
    private String reviewType;
    /**
     * 是否匿名评价
     */
    private Boolean isAnonymous;
    /**
     * 是否推荐
     */
    private Boolean isRecommend;
    /**
     * 点赞数
     */
    private Integer likeCount;
    /**
     * 回复内容
     */
    private String replyContent;
    /**
     * 回复时间
     */
    private String replyTime;
    /**
     * 状态：0-隐藏，1-显示
     */
    private Boolean status;
    /**
     * 所属机构ID
     */
    private Long agencyId;
    /**
     * 服务套餐ID
     */
    private Long servicePackageId;

}
