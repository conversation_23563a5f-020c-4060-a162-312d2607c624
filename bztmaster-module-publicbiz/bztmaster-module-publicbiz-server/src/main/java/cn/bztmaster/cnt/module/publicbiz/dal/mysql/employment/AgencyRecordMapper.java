package cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment;

import cn.bztmaster.cnt.framework.mybatis.core.mapper.BaseMapperX;
import cn.bztmaster.cnt.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.AgencyRecordDO;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDate;
import java.util.List;

/**
 * 机构记录Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AgencyRecordMapper extends BaseMapperX<AgencyRecordDO> {

    /**
     * 分页查询机构记录
     *
     * @param recordType 记录类型（可以是单个类型或类型列表）
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param agencyId 机构ID
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 记录列表
     */
    default List<AgencyRecordDO> selectPage(Object recordType, LocalDate startDate, LocalDate endDate, Long agencyId, long offset, int limit) {
        LambdaQueryWrapperX<AgencyRecordDO> queryWrapper = new LambdaQueryWrapperX<AgencyRecordDO>()
                .eqIfPresent(AgencyRecordDO::getAgencyId, agencyId)
                .geIfPresent(AgencyRecordDO::getRecordDate, startDate)
                .leIfPresent(AgencyRecordDO::getRecordDate, endDate)
                .orderByDesc(AgencyRecordDO::getCreateTime)
                .last("LIMIT " + offset + "," + limit);
        
        // 处理记录类型：支持单个类型或类型列表
        if (recordType instanceof String) {
            queryWrapper.eqIfPresent(AgencyRecordDO::getRecordType, (String) recordType);
        } else if (recordType instanceof List) {
            @SuppressWarnings("unchecked")
            List<String> recordTypes = (List<String>) recordType;
            if (recordTypes != null && !recordTypes.isEmpty()) {
                queryWrapper.in(AgencyRecordDO::getRecordType, recordTypes);
            }
        }
        
        return selectList(queryWrapper);
    }

    /**
     * 根据记录编号查询记录
     *
     * @param recordId 记录编号
     * @return 记录
     */
    default AgencyRecordDO selectByRecordId(String recordId) {
        return selectOne(AgencyRecordDO::getRecordId, recordId);
    }

    /**
     * 根据机构ID查询记录列表
     *
     * @param agencyId 机构ID
     * @return 记录列表
     */
    default List<AgencyRecordDO> selectByAgencyId(Long agencyId) {
        return selectList(AgencyRecordDO::getAgencyId, agencyId);
    }

    /**
     * 统计记录总数
     *
     * @param recordType 记录类型（可以是单个类型或类型列表）
     * @param agencyId 机构ID
     * @return 记录总数
     */
    default long selectCount(Object recordType, Long agencyId) {
        LambdaQueryWrapperX<AgencyRecordDO> queryWrapper = new LambdaQueryWrapperX<AgencyRecordDO>()
                .eqIfPresent(AgencyRecordDO::getAgencyId, agencyId);
        
        // 处理记录类型：支持单个类型或类型列表
        if (recordType instanceof String) {
            queryWrapper.eqIfPresent(AgencyRecordDO::getRecordType, (String) recordType);
        } else if (recordType instanceof List) {
            @SuppressWarnings("unchecked")
            List<String> recordTypes = (List<String>) recordType;
            if (recordTypes != null && !recordTypes.isEmpty()) {
                queryWrapper.in(AgencyRecordDO::getRecordType, recordTypes);
            }
        }
        
        return selectCount(queryWrapper);
    }
} 