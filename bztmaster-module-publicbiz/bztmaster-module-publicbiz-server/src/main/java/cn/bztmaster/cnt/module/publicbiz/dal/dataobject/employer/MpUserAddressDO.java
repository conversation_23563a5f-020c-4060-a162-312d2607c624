package cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employer;

import cn.bztmaster.cnt.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;

/**
 * 用户地址表 DO
 *
 * <AUTHOR>
 */
@TableName("mp_user_address")
@KeySequence("mp_user_address_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MpUserAddressDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 地址标签，如：家庭地址、公司地址等
     */
    private String label;

    /**
     * 收货人姓名
     */
    private String receiverName;

    /**
     * 收货人手机号
     */
    private String receiverPhone;

    /**
     * 省份编码
     */
    private String provinceCode;

    /**
     * 省份名称
     */
    private String provinceName;

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 区县编码
     */
    private String districtCode;

    /**
     * 区县名称
     */
    private String districtName;

    /**
     * 省市区组合，如：四川省成都市锦江区
     */
    private String region;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 完整地址（省市区+详细地址）
     */
    private String fullAddress;

    /**
     * 经度
     */
    private BigDecimal longitude;

    /**
     * 纬度
     */
    private BigDecimal latitude;

    /**
     * 是否默认地址：0-否，1-是
     */
    private Boolean isDefault;

    /**
     * 状态：0-禁用，1-启用
     */
    private Boolean status;

    /**
     * 排序，数字越小越靠前
     */
    private Integer sort;

    /**
     * 备注
     */
    private String remark;
}
