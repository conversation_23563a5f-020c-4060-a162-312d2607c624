package cn.bztmaster.cnt.module.publicbiz.service.aunt;

import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.PractitionerDO;

/**
 * 阿姨验证服务接口
 *
 * <AUTHOR>
 */
public interface AuntValidationService {

    /**
     * 验证阿姨信息是否存在
     *
     * @param auntOneId 阿姨OneID
     * @return 阿姨信息，如果不存在则返回null
     */
    PractitionerDO validateAuntExists(String auntOneId);

    /**
     * 验证阿姨信息是否存在，如果不存在则抛出异常
     *
     * @param auntOneId 阿姨OneID
     * @return 阿姨信息
     * @throws RuntimeException 如果阿姨信息不存在
     */
    PractitionerDO validateAuntExistsOrThrow(String auntOneId);
}
