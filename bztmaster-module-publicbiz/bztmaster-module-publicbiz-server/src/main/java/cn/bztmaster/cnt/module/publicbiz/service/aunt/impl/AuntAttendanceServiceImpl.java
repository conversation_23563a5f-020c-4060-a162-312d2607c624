package cn.bztmaster.cnt.module.publicbiz.service.aunt.impl;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.framework.common.util.date.DateUtils;
import cn.bztmaster.cnt.module.publicbiz.controller.app.aunt.vo.*;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.domestic.DomesticTaskDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.PractitionerDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.workorder.WorkOrderDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.domestic.DomesticTaskMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.PractitionerMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.AuntPunchRecordMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.workorder.WorkOrderMapper;
import cn.bztmaster.cnt.module.publicbiz.service.aunt.AuntAttendanceService;
import cn.bztmaster.cnt.module.publicbiz.util.aunt.AuntAttendanceUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AuntAttendanceServiceImpl implements AuntAttendanceService {

    @Resource
    private DomesticTaskMapper domesticTaskMapper;

    @Resource
    private AuntPunchRecordMapper auntPunchRecordMapper;

    @Resource
    private WorkOrderMapper workOrderMapper;

    @Resource
    private PractitionerMapper practitionerMapper;

    @Override
    public AuntAttendanceSummaryRespVO getAttendanceSummary(AuntAttendanceSummaryReqVO reqVO) {
        log.info("查询阿姨考勤统计，oneId: {}, year: {}, month: {}", reqVO.getOneId(), reqVO.getYear(), reqVO.getMonth());

        AuntAttendanceSummaryRespVO respVO = new AuntAttendanceSummaryRespVO();
        respVO.setYear(reqVO.getYear());
        respVO.setMonth(reqVO.getMonth());

        // 计算考勤统计
        calculateAttendanceSummary(reqVO.getOneId(), reqVO.getYear(), reqVO.getMonth(), respVO);

        log.info("阿姨考勤统计查询完成，结果: {}", respVO);
        return respVO;
    }

    @Override
    public AuntAttendanceApplyRespVO submitAttendanceApply(AuntAttendanceApplyReqVO reqVO) {
        log.info("提交阿姨请假/调休申请，reqVO: {}", reqVO);

        // 验证申请时间有效性
        validateApplyTime(reqVO);

        // 生成申请单号
        String applyId = generateApplyId(reqVO);
        
        // 创建工单
        WorkOrderDO workOrder = createWorkOrder(reqVO, applyId);
        workOrderMapper.insert(workOrder);

        AuntAttendanceApplyRespVO respVO = new AuntAttendanceApplyRespVO();
        respVO.setApplyId(applyId);
        respVO.setStatus("PENDING");
        respVO.setCreateTime(LocalDateTime.now());

        log.info("阿姨请假/调休申请提交成功，applyId: {}", applyId);
        return respVO;
    }

    @Override
    public PageResult<AuntAttendanceHistoryRespVO> getAttendanceHistory(AuntAttendanceHistoryReqVO reqVO) {
        log.info("查询阿姨历史请假/调休申请，reqVO: {}", reqVO);

        // 查询请假、调休工单列表
        List<WorkOrderDO> workOrderList = workOrderMapper.selectByAuntOneIdAndType(
                reqVO.getOneId());

        // 转换为VO对象
        List<AuntAttendanceHistoryRespVO> voList = workOrderList.stream()
                .map(this::convertToHistoryRespVO)
                .collect(Collectors.toList());

        // 分页处理
        int total = voList.size();
        int pageNo = reqVO.getPageNo();
        int pageSize = reqVO.getPageSize();
        int start = (pageNo - 1) * pageSize;
        int end = Math.min(start + pageSize, total);

        List<AuntAttendanceHistoryRespVO> pageList = voList.subList(start, end);

        return new PageResult<>(pageList, (long) total);
    }

    @Override
    public AuntAttendanceApproveRespVO approveAttendanceApply(AuntAttendanceApproveReqVO reqVO) {
        log.info("审批阿姨请假/调休申请，reqVO: {}", reqVO);

        // 查询工单
        WorkOrderDO workOrder = workOrderMapper.selectByWorkOrderNo(reqVO.getApplyId());
        if (workOrder == null) {
            throw new RuntimeException("申请单不存在: " + reqVO.getApplyId());
        }

        // 验证工单状态
        if (workOrder.getStatus() != 0) {
            throw new RuntimeException("申请状态不允许修改: " + workOrder.getStatus());
        }

        // 更新工单状态
        Integer newStatus = "APPROVE".equals(reqVO.getAction()) ? 1 : 2;
        workOrder.setStatus(newStatus);
        workOrder.setApproveRemark(reqVO.getRemark());
        workOrder.setApproveTime(LocalDateTime.now());
        workOrderMapper.updateById(workOrder);

        AuntAttendanceApproveRespVO respVO = new AuntAttendanceApproveRespVO();
        respVO.setApplyId(reqVO.getApplyId());
        respVO.setStatus(newStatus.equals(1) ? "APPROVED" : "REJECTED");
        respVO.setApproveTime(LocalDateTime.now());

        log.info("阿姨请假/调休申请审批完成，applyId: {}, status: {}", reqVO.getApplyId(), newStatus);
        return respVO;
    }

    /**
     * 计算考勤统计
     */
    private void calculateAttendanceSummary(String auntOneId, Integer year, Integer month, 
                                         AuntAttendanceSummaryRespVO respVO) {
        // 1. 计算该月份的工作日总数
        int totalWorkDays = AuntAttendanceUtil.calculateWorkDays(year, month);
        respVO.setTotalWorkDays(totalWorkDays);

        // 2. 统计该阿姨在该月份的实际出勤记录
        LocalDate startDate = LocalDate.of(year, month, 1);
        LocalDate endDate = startDate.plusMonths(1).minusDays(1);
        int attendanceDays = auntPunchRecordMapper.countAttendanceDays(auntOneId, startDate, endDate);
        respVO.setAttendanceDays(attendanceDays);

        // 3. 统计该阿姨在该月份的请假申请记录（已批准状态）
        int leaveDays = workOrderMapper.countLeaveDays(auntOneId, year, month);
        respVO.setLeaveDays(leaveDays);

        // 4. 统计该阿姨在该月份的调休申请记录（已批准状态）
        int adjustDays = workOrderMapper.countAdjustDays(auntOneId, year, month);
        respVO.setAdjustDays(adjustDays);

        // 5. 计算已工作天数对应的就是阿姨的出勤天数
        respVO.setWorkedDays(attendanceDays);
    }

    /**
     * 验证申请时间有效性
     */
    private void validateApplyTime(AuntAttendanceApplyReqVO reqVO) {
        // 验证开始时间不能晚于结束时间
        LocalDateTime startTime = LocalDateTime.parse(reqVO.getStartDate() + " " + reqVO.getStartTime(), 
                DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"));
        LocalDateTime endTime = LocalDateTime.parse(reqVO.getEndDate() + " " + reqVO.getEndTime(), 
                DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"));
        
        if (startTime.isAfter(endTime)) {
            throw new RuntimeException("开始时间不能晚于结束时间");
        }
        
        // 验证申请时间不能早于当前时间
        if (startTime.isBefore(LocalDateTime.now())) {
            throw new RuntimeException("申请时间不能早于当前时间");
        }
    }

    /**
     * 检查与现有排班冲突
     */
    private void checkScheduleConflict(AuntAttendanceApplyReqVO reqVO) {
        LocalDate startDate = LocalDate.parse(reqVO.getStartDate());
        LocalDate endDate = LocalDate.parse(reqVO.getEndDate());
        
        // 查询该时间段内的排班任务
        List<DomesticTaskDO> conflictTasks = domesticTaskMapper.selectByAuntOneIdAndDateRange(
                reqVO.getOneId(), startDate, endDate);
        
        if (!conflictTasks.isEmpty()) {
            throw new RuntimeException("申请时间与现有排班冲突，请调整申请时间");
        }
    }

    /**
     * 生成申请单号
     */
    private String generateApplyId(AuntAttendanceApplyReqVO reqVO) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String random = UUID.randomUUID().toString().substring(0, 6);
        return "apply_" + timestamp + random;
    }

    /**
     * 创建工单
     */
    private WorkOrderDO createWorkOrder(AuntAttendanceApplyReqVO reqVO, String applyId) {
        WorkOrderDO workOrder = new WorkOrderDO();
        workOrder.setWorkOrderNo(applyId);
        workOrder.setOrderNo("SYSTEM_" + applyId); // 请假/调休申请不关联具体订单，使用系统生成的订单号
        workOrder.setWorkOrderTitle("LEAVE".equals(reqVO.getApplyType()) ? "请假申请" : "调休申请");
        workOrder.setWorkOrderContent(reqVO.getReason());
        workOrder.setWorkOrderType("LEAVE".equals(reqVO.getApplyType()) ? "take_leave" : "leave_adjustment");
        workOrder.setPriority("medium");
        workOrder.setWorkOrderStatus("pending");
        workOrder.setAuntOneid(reqVO.getOneId());
        // 获取阿姨姓名
        PractitionerDO practitioner = practitionerMapper.selectByAuntOneId(reqVO.getOneId());
        String auntName = practitioner != null ? practitioner.getName() : "";
        workOrder.setAuntName(auntName);
        workOrder.setLeaveType("LEAVE".equals(reqVO.getApplyType()) ? 1 : 2);
        workOrder.setStartTime(LocalDateTime.parse(reqVO.getStartDate() + " " + reqVO.getStartTime(), 
                DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")));
        workOrder.setEndTime(LocalDateTime.parse(reqVO.getEndDate() + " " + reqVO.getEndTime(), 
                DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")));

        //此处要根据上面的起始结束时间计算对应的请假时长和请假天数 先计算出小时数，再除以8得到天数 不足0.5按0.5算超过0.5按1算
        int durationHours = (int) Duration.between(workOrder.getStartTime(), workOrder.getEndTime()).toHours();
        workOrder.setDurationHours(BigDecimal.valueOf(durationHours));
        int durationDays = (int) Math.ceil(durationHours / 8.0);
        workOrder.setDurationDays(BigDecimal.valueOf(durationDays));
        workOrder.setStatus(0);
        
        return workOrder;
    }

    /**
     * 转换为历史申请响应VO
     */
    private AuntAttendanceHistoryRespVO convertToHistoryRespVO(WorkOrderDO workOrder) {
        AuntAttendanceHistoryRespVO respVO = new AuntAttendanceHistoryRespVO();
        respVO.setApplyId(workOrder.getWorkOrderNo());
        respVO.setApplyType(workOrder.getLeaveType() == 1 ? "LEAVE" : "ADJUST");
        respVO.setApplyTypeText(workOrder.getLeaveType() == 1 ? "请假申请" : "调休申请");
        respVO.setStartDate(workOrder.getStartTime().toLocalDate().toString());
        respVO.setStartTime(workOrder.getStartTime().toLocalTime().toString());
        respVO.setEndDate(workOrder.getEndTime().toLocalDate().toString());
        respVO.setEndTime(workOrder.getEndTime().toLocalTime().toString());
        respVO.setDuration(workOrder.getDurationDays().intValue());
        respVO.setReason(workOrder.getWorkOrderContent());
        respVO.setStatus(workOrder.getStatus().toString());
        respVO.setStatusText(getStatusText(workOrder.getStatus()));
        respVO.setCreateTime(workOrder.getCreateTime());
        respVO.setApproveTime(workOrder.getApproveTime());
        respVO.setApproveRemark(workOrder.getApproveRemark());
        
        return respVO;
    }

    /**
     * 获取状态文本
     */
    private String getStatusText(Integer status) {
        switch (status) {
            case 0: return "审批中";
            case 1: return "已批准";
            case 2: return "已驳回";
            default: return "未知状态";
        }
    }
}
