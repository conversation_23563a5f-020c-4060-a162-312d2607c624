package cn.bztmaster.cnt.module.publicbiz.service.aunt.impl;

import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.PractitionerDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.PractitionerMapper;
import cn.bztmaster.cnt.module.publicbiz.service.aunt.AuntValidationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

/**
 * 阿姨验证服务实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class AuntValidationServiceImpl implements AuntValidationService {

    @Resource
    private PractitionerMapper practitionerMapper;

    @Override
    public PractitionerDO validateAuntExists(String auntOneId) {
        if (!StringUtils.hasText(auntOneId)) {
            log.warn("阿姨OneID为空，无法验证阿姨信息");
            return null;
        }

        try {
            PractitionerDO practitioner = practitionerMapper.selectByAuntOneId(auntOneId);
            if (practitioner == null) {
                log.warn("阿姨信息不存在，auntOneId: {}", auntOneId);
                return null;
            }
            
            log.debug("阿姨信息验证成功，auntOneId: {}, 姓名: {}", auntOneId, practitioner.getName());
            return practitioner;
        } catch (Exception e) {
            log.error("验证阿姨信息时发生异常，auntOneId: {}", auntOneId, e);
            return null;
        }
    }

    @Override
    public PractitionerDO validateAuntExistsOrThrow(String auntOneId) {
        PractitionerDO practitioner = validateAuntExists(auntOneId);
        if (practitioner == null) {
            throw new RuntimeException("阿姨信息不存在，auntOneId: " + auntOneId);
        }
        return practitioner;
    }
}
