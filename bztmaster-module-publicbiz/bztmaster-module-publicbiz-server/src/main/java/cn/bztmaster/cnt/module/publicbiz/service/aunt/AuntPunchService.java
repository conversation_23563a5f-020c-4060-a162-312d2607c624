package cn.bztmaster.cnt.module.publicbiz.service.aunt;

import cn.bztmaster.cnt.module.publicbiz.controller.app.aunt.vo.AuntPunchRecordReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.aunt.vo.AuntPunchRecordRespVO;

/**
 * 阿姨打卡 Service 接口
 *
 * <AUTHOR>
 */
public interface AuntPunchService {

    /**
     * 记录打卡
     *
     * @param reqVO 打卡记录请求
     */
    void recordPunch(AuntPunchRecordReqVO reqVO);

    /**
     * 获得打卡记录列表
     *
     * @param auntOneId 阿姨OneID
     * @param scheduleId 排班ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 打卡记录列表
     */
    AuntPunchRecordRespVO getPunchRecordList(String auntOneId, Long scheduleId, String startDate, String endDate);

} 