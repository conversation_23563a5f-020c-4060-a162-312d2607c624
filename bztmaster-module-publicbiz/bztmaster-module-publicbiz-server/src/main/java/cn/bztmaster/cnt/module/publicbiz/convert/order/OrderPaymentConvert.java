package cn.bztmaster.cnt.module.publicbiz.convert.order;

import cn.bztmaster.cnt.module.publicbiz.api.order.dto.OrderCollectionReqDTO;
import cn.bztmaster.cnt.module.publicbiz.api.order.dto.OrderCollectionRespDTO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.OrderCollectionReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.OrderCollectionRespVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.order.PublicbizOrderPaymentDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 订单支付记录 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface OrderPaymentConvert {

    OrderPaymentConvert INSTANCE = Mappers.getMapper(OrderPaymentConvert.class);

    /**
     * 将VO转换为DTO
     *
     * @param reqVO 请求VO
     * @return 请求DTO
     */
    OrderCollectionReqDTO convert(OrderCollectionReqVO reqVO);

    /**
     * 将DTO转换为响应VO
     *
     * @param respDTO 响应DTO
     * @return 响应VO
     */
    OrderCollectionRespVO convert(OrderCollectionRespDTO respDTO);

    /**
     * 将DO转换为响应DTO
     *
     * @param paymentDO 支付记录DO
     * @return 响应DTO
     */
    OrderCollectionRespDTO convert(PublicbizOrderPaymentDO paymentDO);

    /**
     * 将DO列表转换为响应DTO列表
     *
     * @param paymentDOList 支付记录DO列表
     * @return 响应DTO列表
     */
    List<OrderCollectionRespDTO> convertList(List<PublicbizOrderPaymentDO> paymentDOList);
}
