package cn.bztmaster.cnt.module.publicbiz.dal.mysql.employer;

import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employer.AuntReviewDO;
import cn.bztmaster.cnt.framework.mybatis.core.mapper.BaseMapperX;
import cn.bztmaster.cnt.framework.mybatis.core.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * 阿姨评价 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AuntReviewMapper extends BaseMapperX<AuntReviewDO> {

    /**
     * 根据套餐ID查询平均评分
     *
     * @param packageId 套餐ID
     * @return 平均评分
     */
    BigDecimal selectAverageRatingByPackageId(@Param("packageId") Long packageId);

    /**
     * 根据套餐ID列表查询平均评分
     *
     * @param packageIds 套餐ID列表
     * @return 套餐ID和平均评分的映射
     */
    List<AuntReviewDO> selectAverageRatingByPackageIds(@Param("packageIds") List<Long> packageIds);

    /**
     * 根据机构ID查询该机构所有套餐的平均评分
     *
     * @param agencyId 机构ID
     * @return 平均评分
     */
    BigDecimal selectAverageRatingByAgencyId(@Param("agencyId") Long agencyId);

    /**
     * 根据套餐ID查询服务态度平均评分
     *
     * @param packageId 套餐ID
     * @return 服务态度平均评分
     */
    BigDecimal selectAverageAttitudeRatingByPackageId(@Param("packageId") Long packageId);

    /**
     * 根据套餐ID查询技术专业性平均评分
     *
     * @param packageId 套餐ID
     * @return 技术专业性平均评分
     */
    BigDecimal selectAverageProfessionalRatingByPackageId(@Param("packageId") Long packageId);

    /**
     * 根据套餐ID查询责任心平均评分
     *
     * @param packageId 套餐ID
     * @return 责任心平均评分
     */
    BigDecimal selectAverageResponsibilityRatingByPackageId(@Param("packageId") Long packageId);

    /**
     * 根据套餐ID查询三个维度的平均评分
     *
     * @param packageId 套餐ID
     * @return 包含三个维度评分的对象
     */
    AuntReviewDO selectAverageRatingsByPackageId(@Param("packageId") Long packageId);

    /**
     * 根据服务套餐ID分页查询评价列表
     *
     * @param servicePackageId 服务套餐ID
     * @param offset           偏移量
     * @param limit            限制数量
     * @return 评价列表
     */
    default List<AuntReviewDO> selectPageByServicePackageId(Long servicePackageId, Long offset, Long limit) {
        return selectList(new LambdaQueryWrapperX<AuntReviewDO>()
                .eq(AuntReviewDO::getServicePackageId, servicePackageId)
                .eq(AuntReviewDO::getStatus, 1)
                .eq(AuntReviewDO::getDeleted, false)
                .orderByDesc(AuntReviewDO::getCreateTime)
                .last("LIMIT " + offset + "," + limit));
    }

    /**
     * 根据服务套餐ID统计评价总数
     *
     * @param servicePackageId 服务套餐ID
     * @return 评价总数
     */
    default Long selectCountByServicePackageId(Long servicePackageId) {
        return selectCount(new LambdaQueryWrapperX<AuntReviewDO>()
                .eq(AuntReviewDO::getServicePackageId, servicePackageId)
                .eq(AuntReviewDO::getStatus, 1)
                .eq(AuntReviewDO::getDeleted, false));
    }
}
