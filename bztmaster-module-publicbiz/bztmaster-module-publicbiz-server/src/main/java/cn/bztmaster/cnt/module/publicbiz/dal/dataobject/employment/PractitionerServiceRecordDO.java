package cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("publicbiz_practitioner_service_record")
@Schema(description = "阿姨服务记录表 DO")
public class PractitionerServiceRecordDO {
    @TableId
    private Long id;
    private Long practitionerOneid;
    private String orderId;
    private Long customerId;
    private String customerName;
    private String serviceType;
    private Date serviceStartTime;
    private Date serviceEndTime;
    private String serviceDuration;
    private String serviceAddress;
    private BigDecimal serviceAmount;
    private BigDecimal practitionerIncome;
    private BigDecimal platformIncome;
    private String orderStatus;
    private BigDecimal customerRating;
    private String customerComment;
    private String remark;
    private Date createTime;
    private Date updateTime;
    private String creator;
    private String updater;
    private Boolean deleted;
    private Long tenantId;
} 