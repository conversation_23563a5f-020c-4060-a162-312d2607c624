package cn.bztmaster.cnt.module.publicbiz.dal.mysql.employer;

import cn.bztmaster.cnt.framework.mybatis.core.mapper.BaseMapperX;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employer.MpUserAddressDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户地址表 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MpUserAddressMapper extends BaseMapperX<MpUserAddressDO> {

    /**
     * 根据用户ID查询地址列表
     *
     * @param userId 用户ID
     * @return 地址列表
     */
    List<MpUserAddressDO> selectByUserId(@Param("userId") Long userId);

    /**
     * 根据用户ID查询默认地址
     *
     * @param userId 用户ID
     * @return 默认地址
     */
    MpUserAddressDO selectDefaultByUserId(@Param("userId") Long userId);

    /**
     * 根据用户ID查询启用的地址列表
     *
     * @param userId 用户ID
     * @return 启用的地址列表
     */
    List<MpUserAddressDO> selectEnabledByUserId(@Param("userId") Long userId);

    /**
     * 根据用户ID和地址ID查询地址详情
     *
     * @param userId    用户ID
     * @param addressId 地址ID
     * @return 地址详情
     */
    MpUserAddressDO selectByUserIdAndAddressId(@Param("userId") Long userId, @Param("addressId") Long addressId);

    /**
     * 设置用户的所有地址为非默认
     *
     * @param userId  用户ID
     * @param updater 更新者
     * @return 更新数量
     */
    int clearDefaultByUserId(@Param("userId") Long userId, @Param("updater") String updater);

    /**
     * 设置指定地址为默认地址
     *
     * @param userId    用户ID
     * @param addressId 地址ID
     * @param updater   更新者
     * @return 更新数量
     */
    int setDefaultByUserIdAndAddressId(@Param("userId") Long userId, @Param("addressId") Long addressId,
            @Param("updater") String updater);
}
