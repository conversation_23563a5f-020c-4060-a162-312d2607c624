package cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.util.List;

/**
 * 雇主创建订单请求 VO
 *
 * <AUTHOR>
 */
@Schema(description = "雇主创建订单请求 VO")
@Data
public class EmployerOrderCreateReqVO {

    @Schema(description = "服务套餐ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1001")
    @NotNull(message = "服务套餐ID不能为空")
    private Long serviceId;

    @Schema(description = "套餐类型：long-term(长周期)、count-card(次卡)", requiredMode = Schema.RequiredMode.REQUIRED, example = "long-term")
    @NotBlank(message = "套餐类型不能为空")
    @Pattern(regexp = "^(long-term|count-card)$", message = "套餐类型只能是long-term或count-card")
    private String packageType;

    @Schema(description = "订单总金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "2999.00")
    @NotNull(message = "订单总金额不能为空")
    @DecimalMin(value = "0.01", message = "订单总金额必须大于0")
    private BigDecimal totalAmount;

    @Schema(description = "支付方式：wechat(微信支付)", requiredMode = Schema.RequiredMode.REQUIRED, example = "wechat")
    @NotBlank(message = "支付方式不能为空")
    @Pattern(regexp = "^wechat$", message = "支付方式只能是wechat")
    private String paymentMethod;

    @Schema(description = "支付保障：secure(安心付)、direct(直接付)", requiredMode = Schema.RequiredMode.REQUIRED, example = "secure")
    @NotBlank(message = "支付保障不能为空")
    @Pattern(regexp = "^(secure|direct)$", message = "支付保障只能是secure或direct")
    private String protectionType;

    @Schema(description = "备注信息，最大200字符", example = "请准时到达，谢谢")
    @Size(max = 200, message = "备注信息不能超过200字符")
    private String remark;

    @Schema(description = "提交人手机号（当前授权雇主手机号）", requiredMode = Schema.RequiredMode.REQUIRED, example = "13800138000")
    @NotBlank(message = "提交人手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String userPhone;

    @Schema(description = "服务地址ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "2001")
    @NotNull(message = "服务地址ID不能为空")
    private Long addressId;

    @Schema(description = "套餐详细信息", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "套餐详细信息不能为空")
    private ServiceInfo serviceInfo;

    // 长周期套餐参数
    @Schema(description = "服务开始日期，格式：YYYY-MM-DD", example = "2025-01-15")
    private String startDate;

    @Schema(description = "每日服务时间，格式：HH:MM-HH:MM", example = "09:00-11:00")
    private String dailyTime;

    @Schema(description = "服务天数", example = "26")
    @Min(value = 1, message = "服务天数必须大于0")
    private Integer serviceDays;

    // 次卡套餐参数
    @Schema(description = "服务时间列表，格式：[\"YYYY-MM-DD HH:MM\"]")
    private List<String> serviceTimes;

    @Schema(description = "已选择的服务次数", example = "3")
    @Min(value = 1, message = "已选择的服务次数必须大于0")
    private Integer serviceCount;

    @Schema(description = "套餐详细信息")
    @Data
    public static class ServiceInfo {

        @Schema(description = "套餐标题", requiredMode = Schema.RequiredMode.REQUIRED, example = "26天深度保洁套餐")
        @NotBlank(message = "套餐标题不能为空")
        private String title;

        @Schema(description = "套餐描述", requiredMode = Schema.RequiredMode.REQUIRED, example = "26天 | 每天1次 | 每次2小时")
        @NotBlank(message = "套餐描述不能为空")
        private String description;

        @Schema(description = "套餐价格", requiredMode = Schema.RequiredMode.REQUIRED, example = "2999.00")
        @NotNull(message = "套餐价格不能为空")
        @DecimalMin(value = "0.01", message = "套餐价格必须大于0")
        private BigDecimal price;

        @Schema(description = "原价", requiredMode = Schema.RequiredMode.REQUIRED, example = "3999.00")
        @NotNull(message = "原价不能为空")
        @DecimalMin(value = "0.01", message = "原价必须大于0")
        private BigDecimal originalPrice;

        @Schema(description = "机构名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "成都保洁服务有限公司")
        @NotBlank(message = "机构名称不能为空")
        private String agencyName;

        @Schema(description = "机构ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "5001")
        @NotNull(message = "机构ID不能为空")
        private Long agencyId;
    }
}
