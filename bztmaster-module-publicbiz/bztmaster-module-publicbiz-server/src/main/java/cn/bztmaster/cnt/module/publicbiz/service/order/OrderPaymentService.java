package cn.bztmaster.cnt.module.publicbiz.service.order;

import cn.bztmaster.cnt.module.publicbiz.api.order.dto.OrderCollectionReqDTO;
import cn.bztmaster.cnt.module.publicbiz.api.order.dto.OrderCollectionRespDTO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.order.PublicbizOrderPaymentDO;

import java.util.List;

/**
 * 订单支付记录 Service 接口
 *
 * <AUTHOR>
 */
public interface OrderPaymentService {

    /**
     * 创建支付记录
     *
     * @param reqDTO 收款请求DTO
     * @return 支付记录ID
     */
    Long createPaymentRecord(OrderCollectionReqDTO reqDTO);

    /**
     * 更新支付记录
     *
     * @param reqDTO 收款请求DTO
     * @return 支付记录ID
     */
    Long updatePaymentRecord(OrderCollectionReqDTO reqDTO);

    /**
     * 根据订单ID查询支付记录列表
     *
     * @param orderId 订单ID
     * @return 支付记录列表
     */
    List<PublicbizOrderPaymentDO> getPaymentRecordsByOrderId(Long orderId);

    /**
     * 根据订单号查询支付记录列表
     *
     * @param orderNo 订单号
     * @return 支付记录列表
     */
    List<PublicbizOrderPaymentDO> getPaymentRecordsByOrderNo(String orderNo);

    /**
     * 确认收款
     *
     * @param reqDTO 收款请求DTO
     * @return 收款响应DTO
     */
    OrderCollectionRespDTO confirmCollection(OrderCollectionReqDTO reqDTO);

    /**
     * 更新收款信息
     *
     * @param reqDTO 收款请求DTO
     * @return 收款响应DTO
     */
    OrderCollectionRespDTO updateCollection(OrderCollectionReqDTO reqDTO);

    /**
     * 生成支付单号
     *
     * @return 支付单号
     */
    String generatePaymentNo();
}
