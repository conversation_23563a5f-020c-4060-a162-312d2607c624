package cn.bztmaster.cnt.module.publicbiz.service.leads.impl;

import cn.bztmaster.cnt.framework.common.exception.ServiceException;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.framework.security.core.util.SecurityFrameworkUtils;
import cn.bztmaster.cnt.module.publicbiz.api.leads.dto.*;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.leads.vo.LeadsPageReqVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.leads.LeadFollowUpLogDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.leads.LeadInfoDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.leads.LeadFollowUpLogMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.leads.LeadInfoMapper;
import cn.bztmaster.cnt.module.publicbiz.enums.leads.LeadStatusEnum;
import cn.bztmaster.cnt.module.publicbiz.service.leads.LeadsService;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

import static cn.bztmaster.cnt.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.bztmaster.cnt.module.publicbiz.enums.ErrorCodeConstants.*;

/**
 * 线索 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class LeadsServiceImpl implements LeadsService {

    @Resource
    private LeadInfoMapper leadInfoMapper;
    @Resource
    private LeadFollowUpLogMapper leadFollowUpLogMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createLead(LeadsSaveReqDTO createReqDTO) {
        // 1. 校验
        validateLeadSave(null, createReqDTO);

        // 2. 生成线索ID
        if (StrUtil.isEmpty(createReqDTO.getLeadId())) {
            String leadId = generateLeadId();
            createReqDTO.setLeadId(leadId);
        }

        // 3. 插入
        LeadInfoDO leadInfo = new LeadInfoDO();
        leadInfo.setLeadId(createReqDTO.getLeadId());
        leadInfo.setCustomerName(createReqDTO.getCustomerName());
        leadInfo.setCustomerPhone(createReqDTO.getCustomerPhone());
        leadInfo.setLeadSource(createReqDTO.getLeadSource());
        leadInfo.setBusinessModule(createReqDTO.getBusinessModule());
        leadInfo.setLeadStatus(createReqDTO.getLeadStatus());
        leadInfo.setCreateMethod(createReqDTO.getCreateMethod());

        // 获取当前登录用户信息
        Long currentUserId = SecurityFrameworkUtils.getLoginUserId();
        String currentUserNickname = SecurityFrameworkUtils.getLoginUserNickname();

        // 设置当前跟进人信息
        if (createReqDTO.getCurrentOwner() != null && createReqDTO.getCurrentOwnerName() != null) {
            // 如果前端传入了跟进人信息，使用前端传入的值
            leadInfo.setCurrentOwner(createReqDTO.getCurrentOwner());
            leadInfo.setCurrentOwnerName(createReqDTO.getCurrentOwnerName());
        } else {
            // 如果前端没有传入跟进人信息，则设置为当前登录用户
            if (currentUserId != null) {
                leadInfo.setCurrentOwner(String.valueOf(currentUserId));
            }
            if (currentUserNickname != null) {
                leadInfo.setCurrentOwnerName(currentUserNickname);
            }
        }

        // 设置创建人姓名
        if (currentUserNickname != null) {
            leadInfo.setCreatorName(currentUserNickname);
        }

        leadInfo.setRemark(createReqDTO.getRemark());
        leadInfoMapper.insert(leadInfo);

        // 4. 创建跟进记录
        String logUserNickname = currentUserNickname != null ? currentUserNickname : "系统用户";

        LeadFollowUpLogDO followUpLog = new LeadFollowUpLogDO();
        followUpLog.setLeadId(createReqDTO.getLeadId());
        followUpLog.setFollowUpContent("由 " + logUserNickname + " 创建了该线索");
        followUpLog.setCreatorName(logUserNickname); // 设置创建人姓名
        leadFollowUpLogMapper.insert(followUpLog);

        // 5. 返回
        return leadInfo.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateLead(LeadsSaveReqDTO updateReqDTO) {
        // 1. 校验存在
        validateLeadExists(updateReqDTO.getId());
        // 2. 校验
        validateLeadSave(updateReqDTO.getId(), updateReqDTO);

        // 3. 更新
        LeadInfoDO updateObj = new LeadInfoDO();
        updateObj.setId(updateReqDTO.getId());
        updateObj.setCustomerName(updateReqDTO.getCustomerName());
        updateObj.setCustomerPhone(updateReqDTO.getCustomerPhone());
        updateObj.setLeadSource(updateReqDTO.getLeadSource());
        updateObj.setBusinessModule(updateReqDTO.getBusinessModule());
        updateObj.setLeadStatus(updateReqDTO.getLeadStatus());
        updateObj.setCreateMethod(updateReqDTO.getCreateMethod());

        // 设置当前跟进人信息（由前端传入）
        updateObj.setCurrentOwner(updateReqDTO.getCurrentOwner());
        updateObj.setCurrentOwnerName(updateReqDTO.getCurrentOwnerName());

        updateObj.setRemark(updateReqDTO.getRemark());
        leadInfoMapper.updateById(updateObj);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteLead(Long id) {
        // 校验存在
        validateLeadExists(id);
        // 删除
        leadInfoMapper.deleteById(id);
    }

    @Override
    public LeadInfoDO getLead(Long id) {
        return leadInfoMapper.selectById(id);
    }

    @Override
    public LeadInfoDO getLeadByLeadId(String leadId) {
        return leadInfoMapper.selectByLeadId(leadId);
    }

    @Override
    public List<LeadInfoDO> getLeadList(LeadsListReqDTO listReqDTO) {
        return leadInfoMapper.selectList(buildQueryWrapper(listReqDTO));
    }

    @Override
    public PageResult<LeadInfoDO> getLeadPage(LeadsPageReqDTO pageReqDTO) {
        // 将 DTO 转换为 VO
        LeadsPageReqVO reqVO = new LeadsPageReqVO();
        reqVO.setPageNo(pageReqDTO.getPageNo());
        reqVO.setPageSize(pageReqDTO.getPageSize());
        reqVO.setKeyword(pageReqDTO.getKeyword()); // 设置关键词搜索
        reqVO.setCustomerName(pageReqDTO.getCustomerName());
        reqVO.setCustomerPhone(pageReqDTO.getCustomerPhone());
        reqVO.setLeadSource(pageReqDTO.getLeadSource());
        reqVO.setBusinessModule(pageReqDTO.getBusinessModule());
        reqVO.setLeadStatus(pageReqDTO.getLeadStatus());
        reqVO.setCreateMethod(pageReqDTO.getCreateMethod());
        reqVO.setCurrentOwner(pageReqDTO.getCurrentOwner());
        reqVO.setBeginCreateTime(pageReqDTO.getBeginCreateTime());
        reqVO.setEndCreateTime(pageReqDTO.getEndCreateTime());

        return leadInfoMapper.selectPage(reqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void assignLead(LeadsAssignReqDTO assignReqDTO) {
        // 1. 根据ID查询线索信息并设置leadId
        LeadInfoDO leadInfo = leadInfoMapper.selectById(assignReqDTO.getId());
        if (leadInfo == null) {
            throw exception(LEAD_NOT_EXISTS);
        }
        // 设置leadId到DTO中
        assignReqDTO.setLeadId(leadInfo.getLeadId());

        // 2. 更新线索的跟进人
        LeadInfoDO updateObj = new LeadInfoDO();
        updateObj.setId(leadInfo.getId());
        updateObj.setCurrentOwner(String.valueOf(assignReqDTO.getUserId()));
        updateObj.setCurrentOwnerName(assignReqDTO.getUserName()); // 设置跟进人姓名
        // 如果线索状态是未处理，则更新为跟进中
        if (LeadStatusEnum.UNPROCESSED.getType().equals(leadInfo.getLeadStatus())) {
            updateObj.setLeadStatus(LeadStatusEnum.FOLLOWING_UP.getType());
        }
        leadInfoMapper.updateById(updateObj);

        // 3. 添加分配日志到跟进记录表
        String assignerName = SecurityFrameworkUtils.getLoginUserNickname();
        if (assignerName == null) {
            assignerName = "系统";
        }

        LeadFollowUpLogDO followUpLog = new LeadFollowUpLogDO();
        followUpLog.setLeadId(leadInfo.getLeadId());
        followUpLog.setFollowUpContent(assignerName + " 分配给 " + assignReqDTO.getUserName());
        followUpLog.setCreatorName(assignerName); // 设置创建人姓名
        leadFollowUpLogMapper.insert(followUpLog);

        // 4. 记录分配历史日志
        log.info("[assignLead][线索({})被分配给用户({})][分配备注: {}]", assignReqDTO.getLeadId(),
                assignReqDTO.getUserName(), assignReqDTO.getAssignRemark());
    }

    /**
     * 校验线索是否存在
     *
     * @param id 线索编号
     */
    private void validateLeadExists(Long id) {
        if (id == null) {
            return;
        }
        LeadInfoDO leadInfo = leadInfoMapper.selectById(id);
        if (leadInfo == null) {
            throw exception(LEAD_NOT_EXISTS);
        }
    }

    /**
     * 校验线索保存的合法性
     *
     * @param id 线索编号
     * @param reqDTO 请求DTO
     */
    private void validateLeadSave(Long id, LeadsSaveReqDTO reqDTO) {
        // 如果是更新操作，校验线索ID是否存在
        if (id != null) {
            LeadInfoDO leadInfo = leadInfoMapper.selectById(id);
            if (leadInfo == null) {
                throw exception(LEAD_NOT_EXISTS);
            }
        }

        // 如果提供了线索ID，校验是否重复
        if (StrUtil.isNotEmpty(reqDTO.getLeadId())) {
            LeadInfoDO existingLead = leadInfoMapper.selectByLeadId(reqDTO.getLeadId());
            if (existingLead != null && !existingLead.getId().equals(id)) {
                throw exception(LEAD_ID_EXISTS);
            }
        }

        // 校验客户联系电话是否重复
        if (StrUtil.isNotEmpty(reqDTO.getCustomerPhone())) {
            List<LeadInfoDO> existingLeads = leadInfoMapper.selectListByCustomerPhone(reqDTO.getCustomerPhone());
            if (existingLeads != null && !existingLeads.isEmpty()) {
                // 如果是更新操作，需要排除当前记录
                if (id != null) {
                    existingLeads = existingLeads.stream()
                            .filter(lead -> !lead.getId().equals(id))
                            .collect(Collectors.toList());
                }
                // 如果还有其他记录使用了相同的联系电话，则抛出异常
                if (!existingLeads.isEmpty()) {
                    throw exception(LEAD_CUSTOMER_PHONE_EXISTS);
                }
            }
        }
    }

    /**
     * 构建查询条件
     *
     * @param reqDTO 请求DTO
     * @return 查询条件
     */
    private cn.bztmaster.cnt.framework.mybatis.core.query.LambdaQueryWrapperX<LeadInfoDO> buildQueryWrapper(Object reqDTO) {
        cn.bztmaster.cnt.framework.mybatis.core.query.LambdaQueryWrapperX<LeadInfoDO> queryWrapper = new cn.bztmaster.cnt.framework.mybatis.core.query.LambdaQueryWrapperX<>();
        
        if (reqDTO instanceof LeadsPageReqDTO) {
            LeadsPageReqDTO pageReqDTO = (LeadsPageReqDTO) reqDTO;
            queryWrapper.likeIfPresent(LeadInfoDO::getCustomerName, pageReqDTO.getCustomerName())
                    .likeIfPresent(LeadInfoDO::getCustomerPhone, pageReqDTO.getCustomerPhone())
                    .eqIfPresent(LeadInfoDO::getLeadSource, pageReqDTO.getLeadSource())
                    .eqIfPresent(LeadInfoDO::getBusinessModule, pageReqDTO.getBusinessModule())
                    .eqIfPresent(LeadInfoDO::getLeadStatus, pageReqDTO.getLeadStatus())
                    .eqIfPresent(LeadInfoDO::getCreateMethod, pageReqDTO.getCreateMethod())
                    .eqIfPresent(LeadInfoDO::getCurrentOwner, pageReqDTO.getCurrentOwner())
                    .betweenIfPresent(LeadInfoDO::getCreateTime, pageReqDTO.getBeginCreateTime(), pageReqDTO.getEndCreateTime());
        } else if (reqDTO instanceof LeadsListReqDTO) {
            LeadsListReqDTO listReqDTO = (LeadsListReqDTO) reqDTO;
            queryWrapper.likeIfPresent(LeadInfoDO::getCustomerName, listReqDTO.getCustomerName())
                    .likeIfPresent(LeadInfoDO::getCustomerPhone, listReqDTO.getCustomerPhone())
                    .eqIfPresent(LeadInfoDO::getLeadSource, listReqDTO.getLeadSource())
                    .eqIfPresent(LeadInfoDO::getBusinessModule, listReqDTO.getBusinessModule())
                    .eqIfPresent(LeadInfoDO::getLeadStatus, listReqDTO.getLeadStatus())
                    .eqIfPresent(LeadInfoDO::getCreateMethod, listReqDTO.getCreateMethod())
                    .eqIfPresent(LeadInfoDO::getCurrentOwner, listReqDTO.getCurrentOwner());
        }
        
        return queryWrapper.orderByDesc(LeadInfoDO::getId);
    }

    /**
     * 生成线索ID
     * 格式：LEAD + 年月日 + 3位序列号
     *
     * @return 线索ID
     */
    private String generateLeadId() {
        String datePrefix = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS"));
        String randomSuffix = String.format("%03d", IdUtil.getSnowflake().nextId() % 1000);
        return "XS" + datePrefix + randomSuffix;
    }
}