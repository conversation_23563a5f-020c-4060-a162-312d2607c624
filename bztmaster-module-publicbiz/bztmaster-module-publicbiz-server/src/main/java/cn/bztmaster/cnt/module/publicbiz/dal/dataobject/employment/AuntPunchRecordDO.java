package cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment;

import cn.bztmaster.cnt.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 阿姨打卡记录表 DO
 *
 * <AUTHOR>
 */
@TableName("publicbiz_aunt_punch_record")
@KeySequence("publicbiz_aunt_punch_record_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "阿姨打卡记录表 DO")
public class AuntPunchRecordDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 排班ID
     */
    private Long scheduleId;

    /**
     * 阿姨的OneID GUID
     */
    private String auntOneid;

    /**
     * 阿姨姓名
     */
    private String auntName;

    /**
     * 打卡类型(1-开始打卡,2-完成打卡)
     */
    private Integer punchType;

    /**
     * 打卡时间
     */
    private LocalDateTime punchTime;

    /**
     * 打卡位置
     */
    private String punchLocation;

    /**
     * 打卡纬度
     */
    private BigDecimal punchLatitude;

    /**
     * 打卡经度
     */
    private BigDecimal punchLongitude;

    /**
     * 照片数量
     */
    private Integer photoCount;

    /**
     * 照片URL列表，用逗号分隔
     */
    private String photoUrls;

    /**
     * 打卡备注
     */
    private String remark;
} 