package cn.bztmaster.cnt.module.publicbiz.service.employer.impl;

import cn.bztmaster.cnt.framework.common.exception.ServiceException;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.framework.common.util.object.BeanUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import cn.bztmaster.cnt.framework.common.pojo.PageParam;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.*;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employer.MpUserAddressDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employer.MpUserAddressMapper;
import cn.bztmaster.cnt.module.publicbiz.service.employer.EmployerAddressService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

@Service
@Slf4j
public class EmployerAddressServiceImpl implements EmployerAddressService {

    @Resource
    private MpUserAddressMapper mpUserAddressMapper;

    @Override
    public AddressListRespVO getAddressList(Long userId, Integer page, Integer size, Integer status) {
        if (page == null || page <= 0)
            page = 1;
        if (size == null || size <= 0)
            size = 10;
        if (size > 100)
            size = 100;

        LambdaQueryWrapper<MpUserAddressDO> queryWrapper = new LambdaQueryWrapper<MpUserAddressDO>()
                .eq(MpUserAddressDO::getUserId, userId);
        if (status != null) {
            queryWrapper.eq(MpUserAddressDO::getStatus, status == 1);
        }
        queryWrapper.orderByDesc(MpUserAddressDO::getIsDefault)
                .orderByAsc(MpUserAddressDO::getSort)
                .orderByDesc(MpUserAddressDO::getCreateTime);

        PageParam pageParam = new PageParam();
        pageParam.setPageNo(page);
        pageParam.setPageSize(size);
        PageResult<MpUserAddressDO> pageResult = mpUserAddressMapper.selectPage(pageParam, queryWrapper);

        AddressListRespVO result = new AddressListRespVO();
        result.setTotal(pageResult.getTotal());
        result.setRecords(BeanUtils.toBean(pageResult.getList(), AddressListRespVO.AddressRespVO.class));

        return result;
    }

    @Override
    public AddressDetailRespVO getAddressDetail(Long userId, Long addressId) {
        MpUserAddressDO addressDO = mpUserAddressMapper.selectByUserIdAndAddressId(userId, addressId);
        if (addressDO == null) {
            throw new ServiceException(1003, "地址不存在");
        }
        return BeanUtils.toBean(addressDO, AddressDetailRespVO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createAddress(Long userId, AddressCreateReqVO createReqVO) {
        validateAddressCount(userId);

        MpUserAddressDO addressDO = BeanUtils.toBean(createReqVO, MpUserAddressDO.class);
        addressDO.setUserId(userId);
        addressDO.setStatus(true);

        if (Boolean.TRUE.equals(createReqVO.getIsDefault())) {
            mpUserAddressMapper.clearDefaultByUserId(userId, String.valueOf(userId));
        }

        mpUserAddressMapper.insert(addressDO);
        return addressDO.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAddress(Long userId, Long addressId, AddressUpdateReqVO updateReqVO) {
        MpUserAddressDO existingAddress = mpUserAddressMapper.selectByUserIdAndAddressId(userId, addressId);
        if (existingAddress == null) {
            throw new ServiceException(1003, "地址不存在");
        }

        MpUserAddressDO updateDO = BeanUtils.toBean(updateReqVO, MpUserAddressDO.class);
        updateDO.setId(addressId);
        updateDO.setUserId(userId);

        if (Boolean.TRUE.equals(updateReqVO.getIsDefault())) {
            mpUserAddressMapper.clearDefaultByUserId(userId, String.valueOf(userId));
        }

        mpUserAddressMapper.updateById(updateDO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteAddress(Long userId, Long addressId) {
        MpUserAddressDO existingAddress = mpUserAddressMapper.selectByUserIdAndAddressId(userId, addressId);
        if (existingAddress == null) {
            throw new ServiceException(1003, "地址不存在");
        }
        mpUserAddressMapper.deleteById(addressId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setDefaultAddress(Long userId, Long addressId) {
        MpUserAddressDO existingAddress = mpUserAddressMapper.selectByUserIdAndAddressId(userId, addressId);
        if (existingAddress == null) {
            throw new ServiceException(1003, "地址不存在");
        }

        mpUserAddressMapper.clearDefaultByUserId(userId, String.valueOf(userId));
        int updateCount = mpUserAddressMapper.setDefaultByUserIdAndAddressId(userId, addressId, String.valueOf(userId));
        if (updateCount == 0) {
            throw new ServiceException(1011, "默认地址设置失败");
        }
    }

    @Override
    public AddressDetailRespVO getDefaultAddress(Long userId) {
        MpUserAddressDO defaultAddress = mpUserAddressMapper.selectDefaultByUserId(userId);
        if (defaultAddress == null) {
            return null;
        }
        return BeanUtils.toBean(defaultAddress, AddressDetailRespVO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDeleteAddress(Long userId, List<Long> addressIds) {
        for (Long addressId : addressIds) {
            MpUserAddressDO existingAddress = mpUserAddressMapper.selectByUserIdAndAddressId(userId, addressId);
            if (existingAddress == null) {
                throw new ServiceException(1003, "地址不存在或不属于当前用户");
            }
        }
        mpUserAddressMapper.deleteBatchIds(addressIds);
    }

    private void validateAddressCount(Long userId) {
        LambdaQueryWrapper<MpUserAddressDO> queryWrapper = new LambdaQueryWrapper<MpUserAddressDO>()
                .eq(MpUserAddressDO::getUserId, userId);
        long count = mpUserAddressMapper.selectCount(queryWrapper);
        if (count >= 20) {
            throw new ServiceException(1010, "用户地址数量已达上限");
        }
    }
}
