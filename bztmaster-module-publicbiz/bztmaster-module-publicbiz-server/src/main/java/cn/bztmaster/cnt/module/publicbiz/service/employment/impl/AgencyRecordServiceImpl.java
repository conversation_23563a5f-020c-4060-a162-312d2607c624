package cn.bztmaster.cnt.module.publicbiz.service.employment.impl;

import cn.bztmaster.cnt.framework.common.exception.util.ServiceExceptionUtil;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.framework.security.core.util.SecurityFrameworkUtils;
import cn.bztmaster.cnt.module.publicbiz.api.employment.dto.*;
import cn.bztmaster.cnt.module.publicbiz.api.employment.enums.AgencyRecordTypeEnum;
import cn.bztmaster.cnt.module.publicbiz.convert.employment.AgencyRecordConvert;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.AgencyRecordAttachmentDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.AgencyRecordDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.AgencyRecordFollowUpDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.AgencyRecordAttachmentMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.AgencyRecordFollowUpMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.AgencyRecordMapper;
import cn.bztmaster.cnt.module.publicbiz.service.employment.AgencyRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import static cn.bztmaster.cnt.module.publicbiz.enums.ErrorCodeConstants.*;

/**
 * 机构记录Service实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class AgencyRecordServiceImpl implements AgencyRecordService {

    @Resource
    private AgencyRecordMapper agencyRecordMapper;

    @Resource
    private AgencyRecordAttachmentMapper agencyRecordAttachmentMapper;

    @Resource
    private AgencyRecordFollowUpMapper agencyRecordFollowUpMapper;

    @Override
    public PageResult<AgencyRecordRespDTO> pageAgencyRecord(AgencyRecordPageReqDTO reqDTO) {
        // 计算偏移量
        long offset = (reqDTO.getPageNum() - 1) * reqDTO.getPageSize();
        
        // 处理记录类型：当类型为"all"时，查询incentive和penalty的数据
        String recordType = reqDTO.getRecordType();
        List<String> recordTypes = null;
        if ("all".equals(recordType)) {
            recordTypes = java.util.Arrays.asList("incentive", "penalty");
        }
        Long aa = reqDTO.getAgencyId();
        // 查询记录列表
        List<AgencyRecordDO> list = agencyRecordMapper.selectPage(
                recordTypes != null ? recordTypes : recordType, 
                reqDTO.getStartDate(), 
                reqDTO.getEndDate(),
                reqDTO.getAgencyId(),
                offset, 
                reqDTO.getPageSize()
        );
        
        // 查询总数
        long total = agencyRecordMapper.selectCount(recordTypes != null ? recordTypes : recordType, reqDTO.getAgencyId());
        
        // 转换为DTO
        List<AgencyRecordRespDTO> dtoList = AgencyRecordConvert.INSTANCE.convertList(list);
        
        // 填充附件信息
        for (AgencyRecordRespDTO dto : dtoList) {
            List<AgencyRecordAttachmentDO> attachments = agencyRecordAttachmentMapper.selectByRecordId(dto.getId());
            dto.setAttachments(AgencyRecordConvert.INSTANCE.convertAttachmentList(attachments));
        }
        
        return new PageResult<>(dtoList, total);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AgencyRecordRespDTO createAgencyRecord(AgencyRecordSaveReqDTO reqDTO) {
        // 自动获取当前登录人所属机构ID
        Long currentUserAgencyId = SecurityFrameworkUtils.getLoginUserPartnerId();
        if (currentUserAgencyId != null) {
            reqDTO.setAgencyId(currentUserAgencyId);
        }
        
        // 自动获取当前登录人信息并赋值给记录人字段
        Long currentUserId = SecurityFrameworkUtils.getLoginUserId();
        String currentUserNickname = SecurityFrameworkUtils.getLoginUserNickname();
        if (currentUserId != null) {
            reqDTO.setRecorderId(currentUserId);
        }
        if (currentUserNickname != null) {
            reqDTO.setRecorderName(currentUserNickname);
        }
        
        // 生成记录编号
        String recordId = generateRecordId();
        
        // 转换为DO
        AgencyRecordDO recordDO = AgencyRecordConvert.INSTANCE.convert(reqDTO);
        recordDO.setRecordId(recordId);
        recordDO.setStatus(reqDTO.getStatus());
        recordDO.setAgencyId(reqDTO.getAgencyId());
        recordDO.setRecorderId(reqDTO.getRecorderId());
        recordDO.setRecorderName(reqDTO.getRecorderName());
        recordDO.setRecordTime(LocalDateTime.now());
        
        // 保存记录
        agencyRecordMapper.insert(recordDO);
        
        // 保存附件
        if (reqDTO.getAttachments() != null && !reqDTO.getAttachments().isEmpty()) {
            saveAttachments(recordDO.getId(), reqDTO.getAttachments());
        }
        
        // 返回结果
        AgencyRecordRespDTO respDTO = new AgencyRecordRespDTO();
        respDTO.setId(recordDO.getId());
        respDTO.setRecordId(recordId);
        respDTO.setRecordType(reqDTO.getRecordType());
        
        return respDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAgencyRecord(AgencyRecordSaveReqDTO reqDTO) {
        // 验证记录是否存在
        AgencyRecordDO existingRecord = agencyRecordMapper.selectById(reqDTO.getId());
        if (existingRecord == null) {
            throw ServiceExceptionUtil.exception(AGENCY_RECORD_NOT_EXISTS);
        }
        
        // 转换为DO
        AgencyRecordDO recordDO = AgencyRecordConvert.INSTANCE.convert(reqDTO);
        recordDO.setUpdateTime(LocalDateTime.now());
        
        // 更新记录
        agencyRecordMapper.updateById(recordDO);
        
        // 删除现有附件并重新保存
        if (reqDTO.getAttachments() != null) {
            agencyRecordAttachmentMapper.deleteByRecordId(reqDTO.getId());
            if (!reqDTO.getAttachments().isEmpty()) {
                saveAttachments(reqDTO.getId(), reqDTO.getAttachments());
            }
        }
    }

    @Override
    public AgencyRecordRespDTO getAgencyRecordDetail(Long id) {
        // 查询记录
        AgencyRecordDO recordDO = agencyRecordMapper.selectById(id);
        if (recordDO == null) {
            throw ServiceExceptionUtil.exception(AGENCY_RECORD_NOT_EXISTS);
        }
        
        // 转换为DTO
        AgencyRecordRespDTO respDTO = AgencyRecordConvert.INSTANCE.convert(recordDO);
        
        // 查询附件
        List<AgencyRecordAttachmentDO> attachments = agencyRecordAttachmentMapper.selectByRecordId(id);
        respDTO.setAttachments(AgencyRecordConvert.INSTANCE.convertAttachmentList(attachments));
        
        return respDTO;
    }

    @Override
    public List<AgencyRecordFollowUpRespDTO> getFollowUpList(Long recordId) {
        // 查询跟进记录
        List<AgencyRecordFollowUpDO> followUpList = agencyRecordFollowUpMapper.selectByRecordId(recordId);
        
        // 转换为DTO
        return AgencyRecordConvert.INSTANCE.convertFollowUpList(followUpList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AgencyRecordFollowUpRespDTO createFollowUp(AgencyRecordFollowUpSaveReqDTO reqDTO) {
        // 验证记录是否存在
        AgencyRecordDO recordDO = agencyRecordMapper.selectById(reqDTO.getRecordId());
        if (recordDO == null) {
            throw ServiceExceptionUtil.exception(AGENCY_RECORD_NOT_EXISTS);
        }
        
        // 自动获取当前登录人信息并赋值给操作人字段
        Long currentUserId = SecurityFrameworkUtils.getLoginUserId();
        String currentUserNickname = SecurityFrameworkUtils.getLoginUserNickname();
        if (currentUserId != null) {
            reqDTO.setOperatorId(currentUserId);
        }
        if (currentUserNickname != null) {
            reqDTO.setOperatorName(currentUserNickname);
        }
        
        // 创建跟进记录
        AgencyRecordFollowUpDO followUpDO = new AgencyRecordFollowUpDO();
        followUpDO.setRecordId(reqDTO.getRecordId());
        followUpDO.setTitle(reqDTO.getTitle() != null ? reqDTO.getTitle() : "跟进记录");
        followUpDO.setDescription(reqDTO.getDescription());
        followUpDO.setFollowUpDate(LocalDate.now());
        followUpDO.setOperatorId(reqDTO.getOperatorId());
        followUpDO.setOperatorName(reqDTO.getOperatorName());
        
        // 保存跟进记录
        agencyRecordFollowUpMapper.insert(followUpDO);
        
        // 返回结果
        AgencyRecordFollowUpRespDTO respDTO = new AgencyRecordFollowUpRespDTO();
        respDTO.setId(followUpDO.getId());
        respDTO.setFollowUpDate(LocalDate.now());
        
        return respDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AgencyRecordRespDTO createCommunication(AgencyRecordCommunicationSaveReqDTO reqDTO) {

        
        // 自动获取当前登录人信息并赋值给记录人字段
        Long currentUserId = SecurityFrameworkUtils.getLoginUserId();
        String currentUserNickname = SecurityFrameworkUtils.getLoginUserNickname();
        if (currentUserId != null) {
            reqDTO.setRecorderId(currentUserId);
        }
        if (currentUserNickname != null) {
            reqDTO.setRecorderName(currentUserNickname);
        }
        
        // 生成记录编号
        String recordId = generateRecordId();
        
        // 创建沟通日志记录
        AgencyRecordDO recordDO = new AgencyRecordDO();
        recordDO.setRecordId(recordId);
        recordDO.setAgencyId(reqDTO.getAgencyId());
        recordDO.setRecordType("communication");
        recordDO.setRecordDate(LocalDate.now());
        recordDO.setTitle(reqDTO.getCommunicationTitle());
        recordDO.setDescription(reqDTO.getCommunicationContent());
        recordDO.setCommunicationType(reqDTO.getCommunicationType());
        recordDO.setCommunicationTitle(reqDTO.getCommunicationTitle());
        recordDO.setCommunicationContent(reqDTO.getCommunicationContent());
        recordDO.setParticipants(reqDTO.getParticipants());
        recordDO.setFollowUpDate(reqDTO.getFollowUpDate());
        recordDO.setFollowUpItem(reqDTO.getFollowUpItem());
        recordDO.setStatus("completed");
        recordDO.setRecordTime(LocalDateTime.now());
        recordDO.setRecorderId(reqDTO.getRecorderId());
        recordDO.setRecorderName(reqDTO.getRecorderName());
        
        // 保存记录
        agencyRecordMapper.insert(recordDO);
        
        // 保存附件
        if (reqDTO.getAttachments() != null && !reqDTO.getAttachments().isEmpty()) {
            saveAttachments(recordDO.getId(), reqDTO.getAttachments());
        }
        
        // 返回结果
        AgencyRecordRespDTO respDTO = new AgencyRecordRespDTO();
        respDTO.setId(recordDO.getId());
        respDTO.setRecordId(recordId);
        respDTO.setRecordType("communication");
        
        return respDTO;
    }

    @Override
    public String generateRecordId() {
        // 生成格式：REC + 年月日 + 4位序号
        String dateStr = LocalDate.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd"));
        String prefix = "REC" + dateStr;
        // 查询当天最大序号
        long maxSeq = 1;
        // TODO: 实现序号生成逻辑
        
        return prefix + String.format("%04d", maxSeq);
    }

    /**
     * 保存附件
     *
     * @param recordId 记录ID
     * @param attachments 附件列表
     */
    private void saveAttachments(Long recordId, List<AgencyRecordAttachmentSaveReqDTO> attachments) {
        for (AgencyRecordAttachmentSaveReqDTO attachment : attachments) {
            AgencyRecordAttachmentDO attachmentDO = new AgencyRecordAttachmentDO();
            attachmentDO.setRecordId(recordId);
            attachmentDO.setFileName(attachment.getFileName());
            attachmentDO.setFileType(attachment.getFileType());
            attachmentDO.setFileSize(attachment.getFileSize());
            attachmentDO.setFileUrl(attachment.getFileUrl());
            attachmentDO.setUploadTime(LocalDateTime.now());
            
            agencyRecordAttachmentMapper.insert(attachmentDO);
        }
    }
} 