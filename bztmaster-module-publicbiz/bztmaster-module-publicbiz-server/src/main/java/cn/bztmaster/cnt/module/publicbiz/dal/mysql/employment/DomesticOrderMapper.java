package cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment;

import cn.bztmaster.cnt.framework.mybatis.core.mapper.BaseMapperX;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.DomesticOrderDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface DomesticOrderMapper extends BaseMapperX<DomesticOrderDO> {

    /**
     * 根据阿姨OneID查询订单列表
     *
     * @param practitionerOneid 阿姨OneID
     * @return 订单列表
     */
    List<DomesticOrderDO> selectByPractitionerOneid(@Param("practitionerOneid") String practitionerOneid);

    /**
     * 根据订单ID查询订单详情
     *
     * @param orderId 订单ID
     * @return 订单详情
     */
    DomesticOrderDO selectByOrderId(@Param("orderId") Long orderId);

    /**
     * 根据客户OneID查询订单列表
     *
     * @param customerOneid 客户OneID
     * @return 订单列表
     */
    List<DomesticOrderDO> selectByCustomerOneid(@Param("customerOneid") String customerOneid);
}