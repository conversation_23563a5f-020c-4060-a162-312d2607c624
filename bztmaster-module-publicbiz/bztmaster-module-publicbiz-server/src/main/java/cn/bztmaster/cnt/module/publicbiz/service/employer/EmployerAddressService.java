package cn.bztmaster.cnt.module.publicbiz.service.employer;

import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.*;

import java.util.List;

/**
 * 雇主端地址 Service 接口
 *
 * <AUTHOR>
 */
public interface EmployerAddressService {

    /**
     * 获取用户地址列表
     *
     * @param userId 用户ID
     * @param page   页码
     * @param size   每页数量
     * @param status 状态筛选
     * @return 地址列表
     */
    AddressListRespVO getAddressList(Long userId, Integer page, Integer size, Integer status);

    /**
     * 获取地址详情
     *
     * @param userId    用户ID
     * @param addressId 地址ID
     * @return 地址详情
     */
    AddressDetailRespVO getAddressDetail(Long userId, Long addressId);

    /**
     * 新增地址
     *
     * @param userId      用户ID
     * @param createReqVO 创建请求
     * @return 地址ID
     */
    Long createAddress(Long userId, AddressCreateReqVO createReqVO);

    /**
     * 更新地址
     *
     * @param userId      用户ID
     * @param addressId   地址ID
     * @param updateReqVO 更新请求
     */
    void updateAddress(Long userId, Long addressId, AddressUpdateReqVO updateReqVO);

    /**
     * 删除地址
     *
     * @param userId    用户ID
     * @param addressId 地址ID
     */
    void deleteAddress(Long userId, Long addressId);

    /**
     * 设置默认地址
     *
     * @param userId    用户ID
     * @param addressId 地址ID
     */
    void setDefaultAddress(Long userId, Long addressId);

    /**
     * 获取用户默认地址
     *
     * @param userId 用户ID
     * @return 默认地址
     */
    AddressDetailRespVO getDefaultAddress(Long userId);

    /**
     * 批量删除地址
     *
     * @param userId     用户ID
     * @param addressIds 地址ID数组
     */
    void batchDeleteAddress(Long userId, List<Long> addressIds);
}
