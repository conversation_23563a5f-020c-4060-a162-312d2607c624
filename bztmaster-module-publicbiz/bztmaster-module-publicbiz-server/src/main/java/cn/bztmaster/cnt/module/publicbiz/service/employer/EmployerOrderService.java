package cn.bztmaster.cnt.module.publicbiz.service.employer;

import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.EmployerOrderListRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.EmployerOrderCreateReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.EmployerOrderCreateRespVO;

import javax.validation.Valid;

/**
 * 雇主端订单 Service 接口
 *
 * <AUTHOR>
 */
public interface EmployerOrderService {

    /**
     * 获取雇主订单列表
     *
     * @param customerOpenId 客户OpenId
     * @param status         订单状态筛选
     * @param page           页码
     * @param size           每页数量
     * @return 订单列表
     */
    EmployerOrderListRespVO getOrderList(String customerOpenId, String status, Integer page, Integer size);

    /**
     * 创建服务套餐订单
     *
     * @param createReqVO 创建订单请求
     * @return 创建订单响应
     */
    EmployerOrderCreateRespVO createOrder(@Valid EmployerOrderCreateReqVO createReqVO);
}
