# 合作伙伴机构同步状态调整说明

## 修改概述

在合作伙伴服务的机构数据同步功能中，调整了新增机构时的默认状态值设置，使通过合作伙伴同步创建的机构与直接创建的机构具有不同的初始状态。

## 修改内容

### 1. 状态设置差异

**通过合作伙伴同步创建的机构：**
- `cooperationStatus` = "cooperating"（合作中）
- `reviewStatus` = "approved"（已通过审核）

**直接通过 AgencyController 创建的机构：**
- `cooperationStatus` = "pending"（待审核）
- `reviewStatus` = "pending"（待审核）

### 2. 实现方式

在 `PartnerServiceImpl.syncAgencyData()` 方法中，在调用 `agencyService.createAgency()` 创建机构后，立即更新机构的状态字段：

```java
// 创建机构记录
Long agencyId = agencyService.createAgency(agencyCreateReqVO);

// 更新通过合作伙伴同步创建的机构状态
// 通过合作伙伴同步创建的机构直接设置为"合作中"和"已通过审核"状态
// 区别于直接通过 AgencyController 创建的机构（默认为"待审核"状态）
AgencyDO createdAgency = agencyMapper.selectById(agencyId);
if (createdAgency != null) {
    createdAgency.setCooperationStatus("cooperating"); // 合作中（区别于默认的"pending"）
    createdAgency.setReviewStatus("approved"); // 已通过审核（区别于默认的"pending"）
    agencyMapper.updateById(createdAgency);
}
```

### 3. 状态枚举值

使用的状态值与系统中定义的枚举值一致：

- **合作状态枚举** (`AgencyCooperationStatusEnum`)：
  - `COOPERATING("cooperating", "合作中")`
  - `PENDING("pending", "待审核")`

- **审核状态枚举** (`AgencyReviewStatusEnum`)：
  - `APPROVED("approved", "已通过")`
  - `PENDING("pending", "待审核")`

## 业务逻辑说明

### 设计原因

1. **业务合理性**：通过合作伙伴同步创建的机构，说明已经在合作伙伴管理中通过了相关审核，因此可以直接设置为"合作中"和"已通过审核"状态。

2. **流程区分**：区分不同的机构创建来源，避免重复审核流程。

3. **状态一致性**：确保机构状态与合作伙伴的合作状态保持一致。

### 影响范围

- **仅影响**：通过 `PartnerServiceImpl.syncAgencyData()` 方法创建的机构
- **不影响**：直接通过 `AgencyController.createAgency` 接口创建的机构
- **不影响**：现有的机构数据和状态

## 测试验证

### 测试用例

1. **新增家政机构合作伙伴**：验证创建的机构状态为"cooperating"和"approved"
2. **新增非家政机构合作伙伴**：验证不会创建机构
3. **直接创建机构**：验证状态仍为默认的"pending"和"pending"

### 测试方法

运行单元测试：
```bash
mvn test -Dtest=PartnerServiceAgencySyncTest
```

## 注意事项

1. **事务一致性**：机构状态更新在同一事务中完成，确保数据一致性
2. **错误处理**：如果状态更新失败，整个合作伙伴创建操作会回滚
3. **日志记录**：状态变更会被记录在操作日志中

## 相关文件

- `PartnerServiceImpl.java` - 主要修改文件
- `PartnerServiceAgencySyncTest.java` - 测试文件
- `AgencyCooperationStatusEnum.java` - 合作状态枚举
- `AgencyReviewStatusEnum.java` - 审核状态枚举
