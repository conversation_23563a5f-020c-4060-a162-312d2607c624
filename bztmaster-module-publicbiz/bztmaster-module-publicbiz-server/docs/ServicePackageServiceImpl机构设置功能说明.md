# ServicePackageServiceImpl 机构设置功能说明

## 修改概述

参考当前代码中设置合作伙伴ID和名称的实现模式，在 ServicePackageServiceImpl 中添加了类似的逻辑来设置机构ID（agency_id）和机构名称（agency_name）字段。

## 实现内容

### 1. 依赖注入

在 ServicePackageServiceImpl 中添加了 AgencyMapper 的注入：

```java
@Resource
private AgencyMapper agencyMapper;
```

同时添加了相关的导入：

```java
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.AgencyMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.AgencyDO;
```

### 2. 创建服务套餐时的机构设置

在 `createServicePackage()` 方法中，在合作伙伴设置逻辑之后添加了机构设置逻辑：

```java
// 设置机构ID和机构名称
Long currentUserAgencyId = SecurityFrameworkUtils.getLoginUserAgencyId();
if (createReqVO.getAgencyId() == null) {
    // 如果前端未传递机构ID，则使用当前登录用户的机构ID
    servicePackage.setAgencyId(currentUserAgencyId);
}

// 设置机构名称
Long finalAgencyId = servicePackage.getAgencyId();
if (finalAgencyId != null) {
    try {
        AgencyDO agency = agencyMapper.selectById(finalAgencyId);
        if (agency != null && !Boolean.TRUE.equals(agency.getDeleted())) {
            servicePackage.setAgencyName(agency.getAgencyName());
        } else {
            servicePackage.setAgencyName(null);
            log.warn("机构ID {} 对应的机构不存在或已删除", finalAgencyId);
        }
    } catch (Exception e) {
        servicePackage.setAgencyName(null);
        log.error("查询机构信息失败，agencyId: {}", finalAgencyId, e);
    }
}
```

### 3. 更新服务套餐时的机构设置

在 `updateServicePackage()` 方法中，在合作伙伴同步逻辑之后添加了机构同步逻辑：

```java
// 同步机构名称（如果agencyId发生变化）
if (updateReqVO.getAgencyId() != null &&
    !Objects.equals(updateReqVO.getAgencyId(), oldServicePackage.getAgencyId())) {
    try {
        AgencyDO agency = agencyMapper.selectById(updateReqVO.getAgencyId());
        if (agency != null && !Boolean.TRUE.equals(agency.getDeleted())) {
            servicePackage.setAgencyName(agency.getAgencyName());
        } else {
            servicePackage.setAgencyName(null);
            log.warn("更新服务套餐时，机构ID {} 对应的机构不存在或已删除", updateReqVO.getAgencyId());
        }
    } catch (Exception e) {
        servicePackage.setAgencyName(null);
        log.error("更新服务套餐时查询机构信息失败，agencyId: {}", updateReqVO.getAgencyId(), e);
    }
}
```

## 功能特性

### 1. 智能机构ID设置

- **前端传递机构ID**：直接使用前端传递的机构ID
- **前端未传递机构ID**：自动使用当前登录用户的机构ID（通过 `SecurityFrameworkUtils.getLoginUserAgencyId()` 获取）

### 2. 机构名称同步

- **创建时**：根据最终确定的机构ID查询机构信息并设置机构名称
- **更新时**：仅当机构ID发生变化时才查询并同步机构名称，避免不必要的数据库查询

### 3. 异常处理和日志记录

- **机构不存在**：将机构名称设置为null并记录警告日志
- **机构已删除**：将机构名称设置为null并记录警告日志
- **查询异常**：将机构名称设置为null并记录错误日志

### 4. 性能优化

- **条件查询**：只有在机构ID不为null时才进行数据库查询
- **变更检测**：更新时只有在机构ID发生变化时才查询机构信息
- **异常容错**：查询失败时不影响主流程，只是机构名称为空

## 实现模式对比

| 功能 | 合作伙伴设置 | 机构设置 |
|------|-------------|----------|
| **ID获取方式** | `SecurityFrameworkUtils.getLoginUserPartnerId()` | `SecurityFrameworkUtils.getLoginUserAgencyId()` |
| **前端未传递时** | 使用当前用户合作伙伴ID | 使用当前用户机构ID |
| **名称字段** | `partnerName` | `agencyName` |
| **查询方法** | `partnerMapper.selectById()` | `agencyMapper.selectById()` |
| **名称来源** | `partner.getName()` | `agency.getAgencyName()` |
| **日志前缀** | "合作伙伴" | "机构" |

## 使用场景

### 1. 创建服务套餐

```java
// 场景1：前端传递了机构ID
ServicePackageSaveReqVO reqVO = new ServicePackageSaveReqVO();
reqVO.setAgencyId(100L); // 使用指定的机构ID
// 系统会查询机构ID=100的机构信息并设置机构名称

// 场景2：前端未传递机构ID
ServicePackageSaveReqVO reqVO = new ServicePackageSaveReqVO();
reqVO.setAgencyId(null); // 未指定机构ID
// 系统会自动使用当前登录用户的机构ID
```

### 2. 更新服务套餐

```java
// 场景1：机构ID发生变化
ServicePackageUpdateReqVO reqVO = new ServicePackageUpdateReqVO();
reqVO.setAgencyId(200L); // 新的机构ID
// 系统会查询新机构信息并更新机构名称

// 场景2：机构ID未变化
ServicePackageUpdateReqVO reqVO = new ServicePackageUpdateReqVO();
reqVO.setAgencyId(100L); // 与原机构ID相同
// 系统不会查询机构信息，保持原有机构名称
```

## 测试验证

提供了完整的单元测试，覆盖以下场景：

1. **创建套餐 - 前端未传递机构ID**：验证使用当前用户机构ID
2. **创建套餐 - 前端传递机构ID**：验证使用前端指定的机构ID
3. **创建套餐 - 机构不存在**：验证机构名称设置为null
4. **创建套餐 - 机构已删除**：验证机构名称设置为null
5. **更新套餐 - 机构ID变更**：验证同步新机构名称
6. **更新套餐 - 机构ID未变更**：验证不查询机构信息

## 注意事项

1. **数据一致性**：确保 ServicePackageDO 中的 agencyId 和 agencyName 字段已存在
2. **权限控制**：建议在业务层面控制用户只能操作自己机构的服务套餐
3. **数据完整性**：机构名称可能为null，前端展示时需要处理空值情况
4. **性能考虑**：大批量操作时建议使用批量查询优化性能

## 相关文件

- `ServicePackageServiceImpl.java` - 主要实现文件
- `ServicePackageDO.java` - 数据模型（已包含agencyId和agencyName字段）
- `ServicePackageSaveReqVO.java` - 创建请求VO（已包含agencyId字段）
- `ServicePackageUpdateReqVO.java` - 更新请求VO（已包含agencyId字段）
- `ServicePackageAgencySettingTest.java` - 单元测试文件

## 版本兼容性

- **向后兼容**：新增功能不影响现有的服务套餐管理流程
- **数据兼容**：现有服务套餐的机构信息可以通过数据迁移或手动设置补充
- **接口兼容**：前端可以选择性传递agencyId，系统会智能处理
