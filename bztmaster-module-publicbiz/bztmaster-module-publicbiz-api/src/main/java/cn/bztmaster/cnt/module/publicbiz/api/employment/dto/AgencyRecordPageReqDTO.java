package cn.bztmaster.cnt.module.publicbiz.api.employment.dto;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;

/**
 * 机构记录分页请求DTO
 *
 * <AUTHOR>
 */
@Data
public class AgencyRecordPageReqDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 页码，从1开始
     */
    @NotNull(message = "页码不能为空")
    @Min(value = 1, message = "页码最小为1")
    private Integer pageNum;

    /**
     * 每页记录数，最大100
     */
    @NotNull(message = "每页记录数不能为空")
    @Min(value = 1, message = "每页记录数最小为1")
    @Max(value = 100, message = "每页记录数最大为100")
    private Integer pageSize;

    /**
     * 记录类型：incentive-激励记录，penalty-处罚记录，communication-沟通日志
     */
    private String recordType;

    /**
     * 开始日期
     */
    private LocalDate startDate;

    /**
     * 结束日期
     */
    private LocalDate endDate;

    /**
     * 机构ID
     */
    private Long agencyId;
} 