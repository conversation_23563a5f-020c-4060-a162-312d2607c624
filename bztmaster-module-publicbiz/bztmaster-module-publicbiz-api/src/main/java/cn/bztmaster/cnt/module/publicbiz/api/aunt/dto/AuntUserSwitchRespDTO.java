package cn.bztmaster.cnt.module.publicbiz.api.aunt.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 阿姨用户切换响应 DTO
 *
 * <AUTHOR>
 */
@Data
public class AuntUserSwitchRespDTO {

    /**
     * 是否已注册家政人员
     */
    private Boolean isRegistered;

    /**
     * 阿姨OneID
     */
    private String auntOneId;

    /**
     * 申请单ID
     */
    private String applicationId;

    /**
     * 阿姨姓名
     */
    private String name;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 身份证号
     */
    private String idCard;

    /**
     * 申请时间
     */
    private LocalDateTime submitTime;

    /**
     * 主要服务类型
     */
    private String serviceType;

    /**
     * 从业年限
     */
    private Integer experienceYears;

    /**
     * 平台状态
     */
    private String platformStatus;

    /**
     * 评级
     */
    private BigDecimal rating;

    /**
     * 状态
     */
    private String status;

    /**
     * 当前状态
     */
    private String currentStatus;

}
