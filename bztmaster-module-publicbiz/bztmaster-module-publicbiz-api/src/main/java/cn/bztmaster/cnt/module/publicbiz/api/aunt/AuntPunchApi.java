package cn.bztmaster.cnt.module.publicbiz.api.aunt;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.module.publicbiz.api.aunt.dto.AuntPunchRecordDTO;
import cn.bztmaster.cnt.module.publicbiz.api.aunt.dto.AuntPunchRecordReqDTO;

import java.util.List;

/**
 * 阿姨打卡 API 接口
 *
 * <AUTHOR>
 */
public interface AuntPunchApi {

    /**
     * 记录打卡
     *
     * @param reqDTO 打卡记录请求
     * @return 操作结果
     */
    CommonResult<Boolean> recordPunch(AuntPunchRecordReqDTO reqDTO);

    /**
     * 获得打卡记录列表
     *
     * @param auntOneId 阿姨OneID
     * @param scheduleId 排班ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 打卡记录列表
     */
    CommonResult<List<AuntPunchRecordDTO>> getPunchRecordList(String auntOneId, Long scheduleId, 
                                                             String startDate, String endDate);

} 