-- 新增表信息
服务套餐主表：publicbiz_service_package
服务套餐轮播图表：publicbiz_package_carousel
服务套餐特色标签表：publicbiz_package_feature

-- 阿姨管理相关表
阿姨基本信息表：publicbiz_practitioner
阿姨资质文件表：publicbiz_practitioner_qualification
阿姨服务记录表：publicbiz_practitioner_service_record
阿姨评级记录表：publicbiz_practitioner_rating_record

-- ==================== 剩余表信息汇总 ====================

-- 服务分类相关表
服务分类表：publicbiz_service_category

-- 阿姨评价相关表
阿姨评价表：publicbiz_aunt_review

-- 机构管理相关表
机构主表：publicbiz_agency
机构资质文件表：publicbiz_agency_qualification

-- 任务管理相关表
任务工单表：publicbiz_work_order
工单附件表：publicbiz_work_order_attachment
工单处理日志表：publicbiz_work_order_log
工单流程进度表：publicbiz_work_order_process

-- 内容管理相关表
轮播图表：publicbiz_carousel
资讯主表：publicbiz_news

-- 订单中心相关表
订单主表：publicbiz_order
订单支付记录表：publicbiz_order_payment
高校实践订单详情表：publicbiz_practice_order
企业培训订单详情表：publicbiz_training_order
个人培训订单详情表：publicbiz_personal_order
家政服务订单详情表：publicbiz_domestic_order
家政服务任务表：publicbiz_domestic_task

-- 结算中心相关表
结算记录表：publicbiz_settlement
结算明细表：publicbiz_settlement_detail
机构结算表：publicbiz_agency_settlement
阿姨结算表：publicbiz_practitioner_settlement

-- 系统管理相关表
系统公告表：publicbiz_announcement

-- 对账单管理相关表
对账单主表：publicbiz_reconciliation_statement
对账单明细表：publicbiz_reconciliation_detail
发票管理表：publicbiz_invoice

-- ==================== 表功能说明 ====================

-- 服务套餐模块
-- publicbiz_service_package: 服务套餐主表，存储套餐基本信息、价格、服务内容等
-- publicbiz_package_carousel: 服务套餐轮播图表，存储套餐的轮播图片
-- publicbiz_package_feature: 服务套餐特色标签表，存储套餐的特色标签
-- publicbiz_service_category: 服务分类表，存储服务分类信息

-- 阿姨管理模块
-- publicbiz_practitioner: 阿姨基本信息表，存储阿姨的基本信息、服务类型、评级等
-- publicbiz_practitioner_qualification: 阿姨资质文件表，存储阿姨的各类资质文件
-- publicbiz_practitioner_service_record: 阿姨服务记录表，存储阿姨的服务历史记录
-- publicbiz_practitioner_rating_record: 阿姨评级记录表，存储阿姨评级变更历史
-- publicbiz_aunt_review: 阿姨评价表，存储客户对阿姨的评价信息

-- 机构管理模块
-- publicbiz_agency: 机构主表，存储机构的基本信息、合作状态、审核信息等
-- publicbiz_agency_qualification: 机构资质文件表，存储机构的各类资质文件

-- 任务管理模块
-- publicbiz_work_order: 任务工单表，存储投诉、换人申请、请假等工单信息
-- publicbiz_work_order_detail: 任务工单详情表，存储不同类型工单的详细信息
-- publicbiz_work_order_party: 工单关联方信息表，存储工单相关的各方信息
-- publicbiz_work_order_attachment: 工单附件表，存储工单相关的附件文件
-- publicbiz_work_order_log: 工单处理日志表，存储工单处理过程日志
-- publicbiz_work_order_process: 工单流程进度表，存储工单处理流程进度

-- 内容管理模块
-- publicbiz_carousel: 轮播图表，存储首页轮播图信息
-- publicbiz_news: 资讯主表，存储新闻资讯信息

-- 订单中心模块
-- publicbiz_order: 订单主表，存储订单的基本信息、状态、金额等
-- publicbiz_order_payment: 订单支付记录表，存储订单的支付记录
-- publicbiz_practice_order: 高校实践订单详情表，存储高校实践订单的详细信息
-- publicbiz_training_order: 企业培训订单详情表，存储企业培训订单的详细信息
-- publicbiz_personal_order: 个人培训订单详情表，存储个人培训订单的详细信息
-- publicbiz_domestic_order: 家政服务订单详情表，存储家政服务订单的详细信息
-- publicbiz_domestic_task: 家政服务任务表，存储家政服务的具体任务信息

-- 结算中心模块
-- publicbiz_settlement: 结算记录表，存储各类结算记录
-- publicbiz_settlement_detail: 结算明细表，存储结算的详细项目
-- publicbiz_agency_settlement: 机构结算表，存储机构结算信息
-- publicbiz_practitioner_settlement: 阿姨结算表，存储阿姨结算信息

-- 系统管理模块
-- publicbiz_announcement: 系统公告表，存储系统公告信息

-- 对账单管理模块
-- publicbiz_reconciliation_statement: 对账单主表，存储对账单的基本信息
-- publicbiz_reconciliation_detail: 对账单明细表，存储对账单的详细项目
-- publicbiz_invoice: 发票管理表，存储发票相关信息

-- ==================== 数据库设计说明 ====================

-- 1. 所有表都包含公共字段：id、tenant_id、creator、create_time、updater、update_time、deleted
-- 2. 使用软删除机制，通过deleted字段标记删除状态
-- 3. 支持多租户，通过tenant_id字段区分不同租户
-- 4. 包含完整的审计字段，记录创建和更新信息
-- 5. 建立了合理的索引，提高查询性能
-- 6. 使用外键关联保证数据一致性
-- 7. 字段命名规范，使用下划线命名法
-- 8. 注释完整，便于理解和维护



-- 服务套餐主表
CREATE TABLE `publicbiz_service_package` (
  -- 公共字段
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` BIGINT COMMENT '租户ID',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',

  -- 业务字段
  `name` VARCHAR(200) NOT NULL COMMENT '套餐名称',
  `category_id` BIGINT(20) NULL COMMENT '服务分类ID'
  `category` VARCHAR(50) NOT NULL COMMENT '服务分类：日常保洁/深度保洁/家电清洗/专项服务/月嫂服务/收纳整理',
  `thumbnail` VARCHAR(500) COMMENT '套餐主图URL',
  `price` DECIMAL(10,2) NOT NULL COMMENT '套餐价格',
  `original_price` DECIMAL(10,2) COMMENT '原价',
  `unit` VARCHAR(20) NOT NULL COMMENT '价格单位：次/项/天/月',
  `service_duration` VARCHAR(100) COMMENT '服务时长，如：4小时、26天、90天',
  `package_type` VARCHAR(20) NOT NULL COMMENT '套餐类型：long-term-长周期套餐/count-card-次数次卡套餐',
  `task_split_rule` VARCHAR(200) COMMENT '任务拆分规则',
  `service_description` TEXT COMMENT '服务描述，建议100-200字',
  `service_details` LONGTEXT COMMENT '详细服务内容，富文本格式',
  `service_process` LONGTEXT COMMENT '服务流程，富文本格式',
  `purchase_notice` TEXT COMMENT '购买须知',
  `status` VARCHAR(20) NOT NULL DEFAULT 'pending' COMMENT '状态：active-已上架/pending-待上架/deleted-回收站',

  -- 预约配置字段
  `advance_booking_days` INT NOT NULL DEFAULT 1 COMMENT '预约时间范围：1-提前1天/3-提前3天/7-提前7天',
  `time_selection_mode` VARCHAR(20) NOT NULL DEFAULT '固定时间' COMMENT '时间选择模式：固定时间/灵活时间',
  `appointment_mode` VARCHAR(30) NOT NULL DEFAULT '开始日期预约' COMMENT '预约模式：开始日期预约/一次性预约全部服务次数',
  `service_start_time` VARCHAR(50) NOT NULL DEFAULT '下单后3天内开始' COMMENT '服务开始时间：within-3-days-下单后3天内开始/specified-date-指定日期开始',
  `address_setting` VARCHAR(30) NOT NULL DEFAULT '固定地址' COMMENT '地址设置：固定地址/可变更地址',
  `max_booking_days` INT NOT NULL DEFAULT 30 COMMENT '最大预约天数',
  `cancellation_policy` VARCHAR(500) COMMENT '取消政策',

  -- 索引
  PRIMARY KEY (`id`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_category` (`category`),
  KEY `idx_status` (`status`),
  KEY `idx_package_type` (`package_type`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_deleted` (`deleted`)
) COMMENT='服务套餐主表';

-- 服务套餐表字段补充脚本
-- 为就业服务-服务套餐模块添加审核状态、所属机构ID和所属机构名称字段

-- 1. 添加审核状态字段
ALTER TABLE `publicbiz_service_package`
ADD COLUMN `audit_status` VARCHAR(20) NOT NULL DEFAULT 'auditing' COMMENT '审核状态：pending-待审核， auditing-审核中，approved-已通过，rejected-已拒绝';

-- 2. 添加所属机构ID字段
ALTER TABLE `publicbiz_service_package`
ADD COLUMN `agency_id` BIGINT(20) NULL COMMENT '所属机构ID';

ALTER TABLE `publicbiz_service_package`
ADD COLUMN `category_id` BIGINT(20) NULL COMMENT '服务分类ID';

-- 3. 添加所属机构名称字段
ALTER TABLE `publicbiz_service_package`
ADD COLUMN `agency_name` VARCHAR(100) NULL COMMENT '所属机构名称';

-- 1. 添加拒绝原因字段
ALTER TABLE `publicbiz_service_package`
ADD COLUMN `reject_reason` TEXT NULL COMMENT '拒绝原因，审核拒绝时填写';

-- 4. 为所属机构ID添加索引以提高查询性能
CREATE INDEX `idx_agency_id` ON `publicbiz_service_package` (`agency_id`);

-- 5. 为审核状态添加索引以提高查询性能
CREATE INDEX `idx_audit_status` ON `publicbiz_service_package` (`audit_status`);

-- 6. 更新现有数据的审核状态（可选）
-- 将已上架的套餐设置为已通过审核
UPDATE `publicbiz_service_package` SET `audit_status` = 'approved' WHERE `status` = 'active';

-- 将待上架的套餐设置为审核中
UPDATE `publicbiz_service_package` SET `audit_status` = 'auditing' WHERE `status` = 'pending';

-- 将回收站的套餐设置为已拒绝
UPDATE `publicbiz_service_package` SET `audit_status` = 'rejected' WHERE `status` = 'deleted';


-- 8. 验证字段添加是否成功
SELECT
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_NAME = 'publicbiz_service_package'
AND COLUMN_NAME IN ('audit_status', 'agency_id', 'agency_name');

-- 服务分类表
CREATE TABLE `publicbiz_service_category`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '分类编号',
  `parent_id` bigint(20) NOT NULL COMMENT '父分类编号',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '分类名称',
  `pic_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '移动端分类图',
  `sort` int(11) NULL DEFAULT 0 COMMENT '分类排序',
  `status` tinyint(4) NOT NULL COMMENT '开启状态',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 86 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '商品分类' ROW_FORMAT = Dynamic;



-- 服务套餐轮播图表
CREATE TABLE `publicbiz_package_carousel` (
  -- 公共字段
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` BIGINT COMMENT '租户ID',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',

  -- 业务字段
  `package_id` BIGINT NOT NULL COMMENT '套餐ID',
  `image_url` VARCHAR(500) NOT NULL COMMENT '轮播图URL',
  `sort_order` INT NOT NULL DEFAULT 0 COMMENT '排序，数字越小越靠前',
  `status` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '状态：1-启用，0-禁用',

  -- 索引
  PRIMARY KEY (`id`),
  KEY `idx_package_id` (`package_id`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_status` (`status`),
  KEY `idx_deleted` (`deleted`)
) COMMENT='服务套餐轮播图表';


-- 服务套餐特色标签表
CREATE TABLE `publicbiz_package_feature` (
  -- 公共字段
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` BIGINT COMMENT '租户ID',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',

  -- 业务字段
  `package_id` BIGINT NOT NULL COMMENT '套餐ID',
  `feature_name` VARCHAR(100) NOT NULL COMMENT '特色标签名称',
  `sort_order` INT NOT NULL DEFAULT 0 COMMENT '排序，数字越小越靠前',

  -- 索引
  PRIMARY KEY (`id`),
  KEY `idx_package_id` (`package_id`),
  KEY `idx_feature_name` (`feature_name`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_deleted` (`deleted`)
) COMMENT='服务套餐特色标签表';

-- ==================== 阿姨管理相关表 ====================

-- 阿姨基本信息表
CREATE TABLE `publicbiz_practitioner` (
  -- 公共字段
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` BIGINT COMMENT '租户ID',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  `aunt_oneid` VARCHAR(36) NOT NULL DEFAULT '' COMMENT '阿姨OneID GUID',
  -- 基本信息字段
  `name` VARCHAR(50) NOT NULL COMMENT '阿姨姓名',
  `phone` VARCHAR(20) NOT NULL COMMENT '手机号，用于登录',
  `id_card` VARCHAR(18) NOT NULL COMMENT '身份证号',
  `hometown` VARCHAR(100) COMMENT '籍贯，如：四川成都',
  `age` INT COMMENT '年龄',
  `gender` VARCHAR(10) COMMENT '性别：male-男/female-女',
  `avatar` VARCHAR(500) COMMENT '头像URL',

  -- 服务信息字段
  `service_type` VARCHAR(50) NOT NULL COMMENT '主要服务类型：月嫂/育儿嫂/保洁/护工',
  `experience_years` INT NOT NULL COMMENT '从业年限',
  `platform_status` VARCHAR(20) NOT NULL DEFAULT 'cooperating' COMMENT '平台状态：cooperating-合作中/terminated-已解约',
  `rating` DECIMAL(2,1) NOT NULL DEFAULT 4.5 COMMENT '评级，1.0-5.0',

  -- 机构关联字段
  `agency_id` BIGINT COMMENT '所属机构ID',
  `agency_name` VARCHAR(200) COMMENT '所属机构名称',

  -- 状态字段
  `status` VARCHAR(20) NOT NULL DEFAULT 'active' COMMENT '状态：active-正常/inactive-停用/pending-待审核',
  `current_status` VARCHAR(50) COMMENT '当前状态：服务中/待岗/休假中',
  `current_order_id` VARCHAR(50) COMMENT '当前服务订单ID',

  -- 统计字段
  `total_orders` INT NOT NULL DEFAULT 0 COMMENT '累计服务单数',
  `total_income` DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '累计收入',
  `customer_satisfaction` DECIMAL(3,1) COMMENT '客户满意度评分',

  -- 索引
  PRIMARY KEY (`id`)
) COMMENT='阿姨基本信息表';

-- 阿姨资质文件表
CREATE TABLE `publicbiz_practitioner_qualification` (
  -- 公共字段
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` BIGINT COMMENT '租户ID',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',

  -- 业务字段
  `practitioner_oneId` VARCHAR(36) NOT NULL COMMENT '阿姨oneID',
  `file_type` VARCHAR(50) NOT NULL COMMENT '文件类型：id_card-身份证/health_cert-健康证/skill_cert-专业技能证书/other-其他附件',
  `file_name` VARCHAR(200) NOT NULL COMMENT '文件名',
  `file_url` VARCHAR(500) NOT NULL COMMENT '文件URL',
  `file_size` BIGINT COMMENT '文件大小（字节）',
  `file_extension` VARCHAR(20) COMMENT '文件扩展名',
  `sort_order` INT NOT NULL DEFAULT 0 COMMENT '排序，数字越小越靠前',
  `status` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '状态：1-有效，0-无效',

  -- 索引
  PRIMARY KEY (`id`)
) COMMENT='阿姨资质文件表';

-- 阿姨服务记录表
CREATE TABLE `publicbiz_practitioner_service_record` (
  -- 公共字段
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` BIGINT COMMENT '租户ID',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',

  -- 业务字段
  `practitioner_oneId` BIGINT NOT NULL COMMENT '阿姨OneID',
  `order_id` VARCHAR(50) NOT NULL COMMENT '订单ID',
  `customer_id` BIGINT COMMENT '客户ID',
  `customer_name` VARCHAR(50) COMMENT '客户姓名',
  `service_type` VARCHAR(50) NOT NULL COMMENT '服务类型',
  `service_start_time` DATETIME COMMENT '服务开始时间',
  `service_end_time` DATETIME COMMENT '服务结束时间',
  `service_duration` VARCHAR(50) COMMENT '服务时长',
  `service_address` VARCHAR(500) COMMENT '服务地址',
  `service_amount` DECIMAL(10,2) NOT NULL COMMENT '服务金额',
  `practitioner_income` DECIMAL(10,2) COMMENT '阿姨收入',
  `platform_income` DECIMAL(10,2) COMMENT '平台收入',
  `order_status` VARCHAR(20) NOT NULL COMMENT '订单状态：pending-待确认/confirmed-已确认/in_progress-服务中/completed-已完成/cancelled-已取消',
  `customer_rating` DECIMAL(2,1) COMMENT '客户评分',
  `customer_comment` TEXT COMMENT '客户评价',
  `remark` TEXT COMMENT '备注',

  -- 索引
  PRIMARY KEY (`id`),
  KEY `idx_practitioner_oneId` (`practitioner_oneId`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_customer_id` (`customer_id`),
  KEY `idx_service_type` (`service_type`),
  KEY `idx_service_start_time` (`service_start_time`),
  KEY `idx_order_status` (`order_status`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_deleted` (`deleted`)
) COMMENT='阿姨服务记录表';

-- 阿姨评级记录表
CREATE TABLE `publicbiz_practitioner_rating_record` (
  -- 公共字段
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` BIGINT COMMENT '租户ID',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',

  -- 业务字段
  `practitioner_oneId` BIGINT NOT NULL COMMENT '阿姨OneID',
  `rating_type` VARCHAR(20) NOT NULL COMMENT '评级类型：customer-客户评价/platform-平台评级/system-系统评级',
  `old_rating` DECIMAL(2,1) COMMENT '原评级',
  `new_rating` DECIMAL(2,1) NOT NULL COMMENT '新评级',
  `rating_change` DECIMAL(2,1) COMMENT '评级变化',
  `rating_reason` VARCHAR(500) COMMENT '评级原因',
  `evaluator_id` BIGINT COMMENT '评价人ID',
  `evaluator_name` VARCHAR(50) COMMENT '评价人姓名',
  `evaluator_type` VARCHAR(20) COMMENT '评价人类型：customer-客户/admin-管理员/system-系统',
  `related_order_id` VARCHAR(50) COMMENT '关联订单ID',
  `remark` TEXT COMMENT '备注',

  -- 索引
  PRIMARY KEY (`id`),
  KEY `idx_practitioner_oneId` (`practitioner_oneId`),
  KEY `idx_rating_type` (`rating_type`),
  KEY `idx_evaluator_id` (`evaluator_id`),
  KEY `idx_related_order_id` (`related_order_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_deleted` (`deleted`)
) COMMENT='阿姨评级记录表';


## 9. 阿姨评价表 (publicbiz_aunt_review)

```sql
CREATE TABLE `publicbiz_aunt_review` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `tenant_id` bigint(20) DEFAULT '1' COMMENT '租户ID',
  `order_id` bigint(20) NOT NULL COMMENT '订单ID',
  `aunt_id` bigint(20) NOT NULL COMMENT '阿姨OneID',
  `reviewer_id` bigint(20) NOT NULL COMMENT '评价人用户ID',
  `reviewer_name` varchar(50) NOT NULL COMMENT '评价人姓名',
  `reviewer_avatar` varchar(500) DEFAULT NULL COMMENT '评价人头像URL',
  `rating` decimal(2,1) NOT NULL COMMENT '评分：1.0-5.0',
  `review_tags` text COMMENT '评价标签（JSON格式，如：["专业","细心","守时"]）',
  `review_content` text COMMENT '评价内容',
  `review_images` text COMMENT '评价图片URL列表（JSON格式）',
  `review_type` varchar(20) DEFAULT 'service' COMMENT '评价类型：service-服务评价，attitude-态度评价，professional-专业评价',
  `is_anonymous` tinyint(1) DEFAULT '0' COMMENT '是否匿名评价：0-否，1-是',
  `is_recommend` tinyint(1) DEFAULT '0' COMMENT '是否推荐：0-否，1-是',
  `like_count` int(11) DEFAULT '0' COMMENT '点赞数',
  `reply_content` text COMMENT '回复内容',
  `reply_time` datetime DEFAULT NULL COMMENT '回复时间',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态：0-隐藏，1-显示',
  `creator` varchar(64) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  PRIMARY KEY (`id`)
) COMMENT='阿姨评价表';

   -- 1. 新增三个细分维度评分字段（非空约束，默认值0避免旧数据冲突）
ALTER TABLE `publicbiz_aunt_review`
ADD COLUMN `attitude_rating` DECIMAL(2,1) NOT NULL DEFAULT 0 COMMENT ''服务态度评分（0-5.0）'' AFTER `reviewer_avatar`,
ADD COLUMN `professional_rating` DECIMAL(2,1) NOT NULL DEFAULT 0 COMMENT ''技术专业性评分（0-5.0）'' AFTER `attitude_rating`,
ADD COLUMN `responsibility_rating` DECIMAL(2,1) NOT NULL DEFAULT 0 COMMENT ''责任心评分（0-5.0）'' AFTER `professional_rating`;

-- 2. 将原 `rating` 字段修改为自动计算的总评分（基于三个维度的平均值）
-- 注意：若原 `rating` 字段有数据，需先备份或迁移数据，再修改为计算列
ALTER TABLE `publicbiz_aunt_review`
MODIFY COLUMN `rating` DECIMAL(2,1) GENERATED ALWAYS AS (
  (`attitude_rating` + `professional_rating` + `responsibility_rating`) / 3
) STORED COMMENT ''总评分（自动计算：三个维度评分的平均值）'';

-- 3. （必选）修复历史数据的维度评分（确保总评分与原 `rating` 值一致）
-- 说明：因步骤1中新增维度字段默认值为1.0，需将旧数据的维度评分更新为原总评分的平均值，以保证计算后的 `rating` 不变
UPDATE `publicbiz_aunt_review`
SET
  `attitude_rating` = `rating`,  -- 旧数据维度评分默认等于原总评分（平均分配）
  `professional_rating` = `rating`,
  `responsibility_rating` = `rating`
WHERE `id` > 0;


```
-- ==================== 机构管理相关表 ====================

-- 机构主表
CREATE TABLE `publicbiz_agency` (
  -- 公共字段
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` BIGINT COMMENT '租户ID',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',

  -- 机构基本信息
  `agency_no` VARCHAR(50) NOT NULL COMMENT '机构编号',
  `agency_name` VARCHAR(200) NOT NULL COMMENT '机构全称',
  `agency_short_name` VARCHAR(100) COMMENT '机构简称',
  `agency_type` VARCHAR(30) NOT NULL COMMENT '机构类型：cooperation-合作/competitor-竞争对手/other-其他',

  -- 法人信息
  `legal_representative` VARCHAR(50) COMMENT '法人代表',
  `unified_social_credit_code` VARCHAR(50) COMMENT '统一社会信用代码',
  `establishment_date` DATE COMMENT '成立日期',

  -- 地址信息
  `registered_address` VARCHAR(500) COMMENT '注册地址',
  `operating_address` VARCHAR(500) COMMENT '经营地址',

  -- 经营范围
  `business_scope` TEXT COMMENT '经营范围',

  -- 申请人信息
  `applicant_name` VARCHAR(50) COMMENT '申请人姓名',
  `applicant_phone` VARCHAR(20) COMMENT '申请人电话',
  `application_time` DATETIME COMMENT '申请时间',

  -- 联系信息及地址
  `contact_person` VARCHAR(50) COMMENT '联系人',
  `contact_phone` VARCHAR(20) COMMENT '联系电话',
  `contact_email` VARCHAR(100) COMMENT '联系邮箱',
  `agency_address` VARCHAR(500) COMMENT '机构地址',
  `province_code` VARCHAR(50) COMMENT '省份code',
  `province` VARCHAR(50) COMMENT '省份',
  `city_code` VARCHAR(50) COMMENT '城市code',
  `city` VARCHAR(50) COMMENT '城市',
  `district_code` VARCHAR(50) COMMENT '区县code',
  `district` VARCHAR(50) COMMENT '区县',
  `street_code` VARCHAR(100) COMMENT '街道',
  `street` VARCHAR(100) COMMENT '街道',
  `detail_address` VARCHAR(200) COMMENT '详细地址',

   -- 经纬度信息
  `longitude` DECIMAL(10,7) COMMENT '经度',
  `latitude` DECIMAL(10,7) COMMENT '纬度',
  `location_accuracy` VARCHAR(20) COMMENT '位置精度：high-高精度/medium-中等精度/low-低精度',

  -- 合作信息
  `cooperation_status` VARCHAR(20) NOT NULL DEFAULT 'cooperating' COMMENT '合作状态：cooperating-合作中/suspended-已暂停/terminated-已终止',
  `contract_no` VARCHAR(50) COMMENT '合同编号',
  `contract_start_date` DATE COMMENT '合同开始日期',
  `contract_end_date` DATE COMMENT '合同结束日期',
  `commission_rate` DECIMAL(5,2) COMMENT '佣金比例',

  -- 审核信息
  `review_status` VARCHAR(20) NOT NULL DEFAULT 'pending' COMMENT '审核状态：pending-待审核/approved-已通过/rejected-已拒绝',
  `reviewer` VARCHAR(64) COMMENT '审核人',
  `review_time` DATETIME COMMENT '审核时间',
  `review_remark` TEXT COMMENT '审核备注',

  -- 状态信息
  `status` VARCHAR(20) NOT NULL DEFAULT 'active' COMMENT '状态：active-正常/inactive-停用/pending-待审核',

  -- 备注信息
  `remark` TEXT COMMENT '备注',

  -- 索引
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_agency_no` (`agency_no`),
  UNIQUE KEY `uk_unified_social_credit_code` (`unified_social_credit_code`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_agency_name` (`agency_name`),
  KEY `idx_agency_type` (`agency_type`),
  KEY `idx_legal_representative` (`legal_representative`),
  KEY `idx_establishment_date` (`establishment_date`),
  KEY `idx_province_code` (`province_code`),
  KEY `idx_city_code` (`city_code`),
  KEY `idx_longitude_latitude` (`longitude`, `latitude`),
  KEY `idx_applicant_name` (`applicant_name`),
  KEY `idx_application_time` (`application_time`),
  KEY `idx_cooperation_status` (`cooperation_status`),
  KEY `idx_contract_no` (`contract_no`),
  KEY `idx_review_status` (`review_status`),
  KEY `idx_reviewer` (`reviewer`),
  KEY `idx_review_time` (`review_time`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_deleted` (`deleted`)
) COMMENT='机构主表信息';


-- 机构资质文件表
CREATE TABLE `publicbiz_agency_qualification` (
  -- 公共字段
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` BIGINT COMMENT '租户ID',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',

  -- 业务字段
  `agency_id` BIGINT NOT NULL COMMENT '机构ID',
  `file_type` VARCHAR(50) NOT NULL COMMENT '文件类型：business_license-营业执照/qualification_cert-资质证书/contract-合同文件/other-其他',
  `file_name` VARCHAR(200) NOT NULL COMMENT '文件名',
  `file_url` VARCHAR(500) NOT NULL COMMENT '文件URL',
  `file_size` BIGINT COMMENT '文件大小（字节）',
  `file_extension` VARCHAR(20) COMMENT '文件扩展名',
  `sort_order` INT NOT NULL DEFAULT 0 COMMENT '排序，数字越小越靠前',
  `status` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '状态：1-有效，0-无效',

  -- 索引
  PRIMARY KEY (`id`),
  KEY `idx_agency_id` (`agency_id`),
  KEY `idx_file_type` (`file_type`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_status` (`status`),
  KEY `idx_deleted` (`deleted`)
) COMMENT='机构资质文件表';

-- ==================== 任务管理相关表====================

-- 任务工单表
CREATE TABLE `publicbiz_work_order` (
  -- 公共字段
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` BIGINT COMMENT '租户ID',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',

  -- 工单信息
  `work_order_no` VARCHAR(50) NOT NULL COMMENT '工单编号',
  `order_no` VARCHAR(50) NOT NULL COMMENT '关联订单号',
  `work_order_title` VARCHAR(200) NOT NULL COMMENT '工单标题',
  `work_order_content` TEXT NOT NULL COMMENT '工单内容(投诉内容/请假理由)',
  `work_order_type` VARCHAR(30) NOT NULL COMMENT '工单类型：complaint-投诉/substitution_request-换人申请/take_leave-请假/顶岗/separation_application-离职申请',
  `priority` VARCHAR(20) NOT NULL DEFAULT 'medium' COMMENT '优先级：low-低/medium-中/high-高/urgent-紧急',

  -- 工单状态
  `work_order_status` VARCHAR(20) NOT NULL DEFAULT 'pending' COMMENT '工单状态：pending-待处理/processing-处理中/resolved-已解决/closed-已关闭',

  -- 负责人信息 审批人信息
  `assignee_id` BIGINT COMMENT '负责人ID',
  `assignee_name` VARCHAR(50) COMMENT '负责人姓名',

  -- 阿姨信息 请假、调休申请
  `aunt_oneid` VARCHAR(36) NOT NULL DEFAULT '' COMMENT 'OneID GUID',
  `aunt_name` VARCHAR(64) NOT NULL COMMENT '阿姨姓名',
  `leave_type` TINYINT(1) NOT NULL COMMENT '请假类型(1-请假,2-调休)',
  `start_time` DATETIME NOT NULL COMMENT '开始时间',
  `end_time` DATETIME NOT NULL COMMENT '结束时间',
  `duration_hours` DECIMAL(5,1) NOT NULL COMMENT '请假时长(小时)',
  `duration_days` DECIMAL(3,1) NOT NULL COMMENT '请假天数',
  `status` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '审批状态(0-审批中,1-已批准,2-已驳回)',
  `approve_time` DATETIME DEFAULT NULL COMMENT '审批时间',
  `approve_remark` VARCHAR(500) DEFAULT NULL COMMENT '审批备注/处理意见',

  -- 投诉相关字段
  `complaint_type` VARCHAR(50) COMMENT '投诉类型：service_quality-服务质量/attitude-服务态度/punctuality-守时问题/other-其他',
  `complaint_level` VARCHAR(20) COMMENT '投诉等级：low-轻微/medium-中等/high-严重/urgent-紧急',
  `complaint_time` DATETIME COMMENT '投诉时间',
  `customer_expectation` TEXT COMMENT '客户期望文本内容',
  `complainer_id` BIGINT COMMENT '投诉人ID',
  `complainer_name` VARCHAR(50) COMMENT '投诉人姓名',

  -- 机构相关字段
  `agency_id` BIGINT COMMENT '机构ID',
  `agency_name` VARCHAR(200) COMMENT '机构名称',

  -- 换人申请相关字段
  `reassignment_start_date` DATE COMMENT '重新指派起始日期',
  `new_aunt_oneid` VARCHAR(36) COMMENT '指派新阿姨OneID',
  `new_aunt_name` VARCHAR(50) COMMENT '指派新阿姨名称',
  `reassignment_description` TEXT COMMENT '指派说明内容',
  `reassignment_update_time` DATETIME COMMENT '指派更新时间',
  `reassignment_reason` VARCHAR(100) COMMENT '转派原因(系统基础数据值)',
  `reassignment_remark` TEXT COMMENT '转派备注',

  -- 备注信息
  `remark` VARCHAR(4000) COMMENT '备注',

  -- 索引
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_work_order_no` (`work_order_no`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_deleted` (`deleted`)
) COMMENT='任务工单表';

-- 工单附件表
CREATE TABLE `publicbiz_work_order_attachment` (
  -- 公共字段
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` BIGINT COMMENT '租户ID',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',

  -- 关联工单
  `work_order_no` BIGINT NOT NULL COMMENT '关联工单编号',


  -- 附件信息
  `file_name` VARCHAR(200) NOT NULL COMMENT '文件名',
  `file_url` VARCHAR(500) NOT NULL COMMENT '文件URL',
  `file_type` VARCHAR(50) COMMENT '文件类型（jpg/png/mp3/pdf等）',
  `file_category` VARCHAR(30) COMMENT '文件分类：evidence-证据材料/contract-合同文件/other-其他',
  `upload_purpose` VARCHAR(100) COMMENT '上传目的（如：投诉证据、服务合同等）',

  -- 索引
  PRIMARY KEY (`id`),
  KEY `idx_work_order_no` (`work_order_no`)
) COMMENT='工单附件表';


-- 工单处理日志表
CREATE TABLE `publicbiz_work_order_log` (
  -- 公共字段
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` BIGINT COMMENT '租户ID',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',

  -- 关联工单
  `work_order_no` BIGINT NOT NULL COMMENT '关联工单编号',

  -- 日志信息
  `log_type` VARCHAR(30) NOT NULL COMMENT '日志类型：creation-工单创建/status_change-状态变更/assignment-分配处理人/comment-处理意见/resolution-处理结果',
  `log_title` VARCHAR(200) COMMENT '日志标题',
  `log_content` TEXT COMMENT '日志内容',

  -- 操作人信息
  `operator_id` BIGINT COMMENT '操作人ID',
  `operator_name` VARCHAR(50) COMMENT '操作人姓名',
  `operator_role` VARCHAR(50) COMMENT '操作人角色',

  -- 关联方信息（用于显示如"雇主: 王先生"）
  `related_party_type` VARCHAR(30) COMMENT '关联方类型',
  `related_party_name` VARCHAR(100) COMMENT '关联方名称',

  -- 索引
  PRIMARY KEY (`id`),
  KEY `idx_work_order_no` (`work_order_no`),
  KEY `idx_log_type` (`log_type`),
  KEY `idx_operator_id` (`operator_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_deleted` (`deleted`)
) COMMENT='工单处理日志表';


-- 工单流程进度表
CREATE TABLE `publicbiz_work_order_process` (
  -- 公共字段
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` BIGINT COMMENT '租户ID',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',

  -- 关联工单
  `work_order_no` BIGINT NOT NULL COMMENT '关联工单编号',

  -- 流程信息
  `process_step` VARCHAR(50) NOT NULL COMMENT '流程步骤：submit_application-提交申请/order_handover-订单交接/resignation_approval-离职审批/archive_files-档案归档',
  `step_name` VARCHAR(100) NOT NULL COMMENT '步骤名称（如：提交离职申请）',
  `step_description` VARCHAR(200) COMMENT '步骤描述（如：阿姨提交离职申请）',
  `step_order` INT NOT NULL COMMENT '步骤顺序（1,2,3...）',

  -- 状态信息
  `step_status` VARCHAR(20) NOT NULL DEFAULT 'pending' COMMENT '步骤状态：pending-待处理/processing-处理中/completed-已完成/skipped-已跳过',
  `step_result` VARCHAR(20) COMMENT '步骤结果：approved-同意/rejected-驳回/transferred-转交',

  -- 时间信息
  `expected_start_time` DATETIME COMMENT '预期开始时间',
  `actual_start_time` DATETIME COMMENT '实际开始时间',
  `actual_end_time` DATETIME COMMENT '实际结束时间',

  -- 操作人信息
  `operator_id` BIGINT COMMENT '操作人ID',
  `operator_name` VARCHAR(50) COMMENT '操作人姓名',
  `operator_role` VARCHAR(50) COMMENT '操作人角色',

  -- 备注信息
  `step_remark` TEXT COMMENT '步骤备注',

  -- 索引
  PRIMARY KEY (`id`),
  KEY `idx_work_order_no` (`work_order_no`)
) COMMENT='工单流程进度表';


-- ==================== 内容管理相关表 ====================

-- 轮播图表
CREATE TABLE `publicbiz_carousel` (
  -- 公共字段
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` BIGINT COMMENT '租户ID',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',

  -- 业务字段
  `platform` varchar(20) DEFAULT 'employer' COMMENT '平台：employer-雇主端，aunt-阿姨端',
  `carousel_title` VARCHAR(200) NOT NULL COMMENT '轮播图标题',
  `carousel_image_url` VARCHAR(500) NOT NULL COMMENT '轮播图片URL',
  `carousel_link_url` VARCHAR(500) COMMENT '跳转链接URL',
  `sort_order` INT NOT NULL DEFAULT 0 COMMENT '排序，数字越小越靠前',
  `status` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `start_time` DATETIME COMMENT '开始时间',
  `end_time` DATETIME COMMENT '结束时间',

  -- 索引
  PRIMARY KEY (`id`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_status` (`status`),
  KEY `idx_start_time` (`start_time`),
  KEY `idx_end_time` (`end_time`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_deleted` (`deleted`)
) COMMENT='轮播图表';

-- 资讯主表
CREATE TABLE `publicbiz_news` (
  -- 公共字段
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` BIGINT COMMENT '租户ID',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',

  -- 资讯基本信息
  `news_title` VARCHAR(200) NOT NULL COMMENT '资讯标题',
  `news_summary` TEXT COMMENT '资讯摘要',
  `news_content` LONGTEXT COMMENT '资讯内容',
  `category_id` BIGINT COMMENT '分类ID',
  `category_name` VARCHAR(100) COMMENT '分类名称',

  -- 图片信息
  `cover_image_url` VARCHAR(500) COMMENT '封面图片URL',

  -- 关联素材文章ID
  `material_id` BIGINT COMMENT '关联素材文章ID',
  -- 发布信息
  `author` VARCHAR(50) COMMENT '作者',
  `publish_time` DATETIME COMMENT '发布时间',
  `status` VARCHAR(20) NOT NULL DEFAULT 'draft' COMMENT '状态：draft-草稿/published-已发布/offline-已下架',

  -- 统计信息
  `view_count` INT DEFAULT 0 COMMENT '浏览次数',
  `like_count` INT DEFAULT 0 COMMENT '点赞次数',
  `share_count` INT DEFAULT 0 COMMENT '分享次数',
  `comment_count` bigint(20) DEFAULT '0' COMMENT '评论数',
  `sort` int(11) DEFAULT '0' COMMENT '排序，数字越小越靠前',
  -- 索引
  PRIMARY KEY (`id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_news_title` (`news_title`),
  KEY `idx_author` (`author`),
  KEY `idx_status` (`status`),
  KEY `idx_publish_time` (`publish_time`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_deleted` (`deleted`)
) COMMENT='资讯主表';

-- ==================== 订单中心相关表 ====================

-- 订单主表
CREATE TABLE `publicbiz_order` (
  -- 公共字段
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` BIGINT COMMENT '租户ID',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',

  -- 订单基本信息
  `order_no` VARCHAR(50) NOT NULL COMMENT '订单号',
  `order_type` VARCHAR(30) NOT NULL COMMENT '订单类型：practice-高校实践/training-企业培训/personal-个人培训/domestic-家政服务/certification-考试认证',
  `business_line` VARCHAR(50) NOT NULL COMMENT '业务线：高校实践/企业培训/个人培训与认证/家政服务',

  -- 关联信息
  `opportunity_id` VARCHAR(50) COMMENT '关联商机ID',
  `lead_id` VARCHAR(50) COMMENT '关联线索ID',

  -- 项目信息
  `project_name` VARCHAR(200) COMMENT '项目名称',
  `project_description` TEXT COMMENT '项目描述',
  `start_date` DATE COMMENT '开始日期',
  `end_date` DATE COMMENT '结束日期',

  -- 金额信息
  `total_amount` DECIMAL(12,2) NOT NULL COMMENT '订单总金额',
  `paid_amount` DECIMAL(12,2) NOT NULL DEFAULT 0.00 COMMENT '已支付金额',
  `refund_amount` DECIMAL(12,2) NOT NULL DEFAULT 0.00 COMMENT '退款金额',
  `payment_status` VARCHAR(20) NOT NULL DEFAULT 'pending' COMMENT '支付状态：pending-待支付/paid-已支付/refunded-已退款/cancelled-已取消',

  -- 订单状态
  `order_status` VARCHAR(30) NOT NULL DEFAULT 'draft' COMMENT '订单状态：draft-草稿/pending-待确认/approving-审批中/approved-已批准/rejected-已拒绝/pending_payment-待支付/in_progress-进行中/completed-已完成/cancelled-已取消',

  -- 负责人信息
  `manager_id` BIGINT COMMENT '负责人ID',
  `manager_name` VARCHAR(50) COMMENT '负责人姓名',
  `manager_phone` VARCHAR(20) COMMENT '负责人电话',

  -- 合同信息
  `contract_type` VARCHAR(20) DEFAULT 'electronic' COMMENT '合同类型：electronic-电子合同/paper-纸质合同',
  `contract_file_url` VARCHAR(500) COMMENT '合同文件URL',
  `contract_status` VARCHAR(20) DEFAULT 'unsigned' COMMENT '合同状态：unsigned-未签署/signed-已签署/rejected-已拒绝',

  -- 备注信息
  `remark` TEXT COMMENT '备注',

  -- 结算相关字段
  `settlement_status` VARCHAR(20) NOT NULL DEFAULT 'pending' COMMENT '结算状态：pending-待结算/processing-结算中/completed-已结算/failed-结算失败',
  `settlement_time` DATETIME COMMENT '结算时间',
  `settlement_method` VARCHAR(30) COMMENT '结算方式',
  `is_selected_for_reconciliation` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否被选中生成对账单：0-未选中，1-已选中',
  `selection_time` DATETIME COMMENT '选中时间',
  `selector_id` BIGINT COMMENT '选择人ID',
  `selector_name` VARCHAR(50) COMMENT '选择人姓名',

  -- 索引
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_order_type` (`order_type`),
  KEY `idx_business_line` (`business_line`),
  KEY `idx_payment_status` (`payment_status`),
  KEY `idx_order_status` (`order_status`),
  KEY `idx_settlement_status` (`settlement_status`),
  KEY `idx_is_selected_for_reconciliation` (`is_selected_for_reconciliation`),
  KEY `idx_manager_id` (`manager_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_deleted` (`deleted`)
) COMMENT='订单主表';

-- 订单支付记录表
CREATE TABLE `publicbiz_order_payment` (
  -- 公共字段
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` BIGINT COMMENT '租户ID',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',

  -- 业务字段
  `order_id` BIGINT NOT NULL COMMENT '订单ID',
  `order_no` VARCHAR(50) NOT NULL COMMENT '订单号',
  `payment_no` VARCHAR(50) NOT NULL COMMENT '支付单号',
  `payment_type` VARCHAR(20) NOT NULL COMMENT '支付类型：cash-现金/wechat-微信支付/alipay-支付宝/bank_transfer-银行转账/pos-POS机刷卡/other-其他',
  `payment_amount` DECIMAL(12,2) NOT NULL COMMENT '支付金额',
  `payment_status` VARCHAR(20) NOT NULL COMMENT '支付状态：pending-待支付/success-支付成功/failed-支付失败/cancelled-已取消',
  `payment_time` DATETIME COMMENT '支付时间',
  `operator_id` BIGINT COMMENT '操作人ID',
  `operator_name` VARCHAR(50) COMMENT '操作人姓名',
  `payment_remark` TEXT COMMENT '支付备注',
  `transaction_id` VARCHAR(100) COMMENT '第三方交易号',

  -- 索引
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_payment_no` (`payment_no`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_order_no` (`order_no`),
  KEY `idx_payment_type` (`payment_type`),
  KEY `idx_payment_status` (`payment_status`),
  KEY `idx_payment_time` (`payment_time`),
  KEY `idx_operator_id` (`operator_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_deleted` (`deleted`)
) COMMENT='订单支付记录表';


ALTER TABLE `publicbiz_order_payment`
    ADD COLUMN `is_escrow` bit(1) DEFAULT b'0' COMMENT '资金托管:0 直接付；1 安心付',
  ADD COLUMN `platform_payment_no` varchar(50) COMMENT '资金托管:平台支付单号（第二阶段支付）',
  ADD COLUMN `platform_payment_status` varchar(20) COMMENT '资金托管:平台支付状态',
  ADD COLUMN `platform_payment_time` datetime COMMENT '资金托管:平台支付时间',
  ADD COLUMN `receiver_id` bigint(20) COMMENT '收款方用户ID（服务者）',
	ADD COLUMN `notify_status` varchar(20) DEFAULT 'pending' COMMENT '回调状态：pending/success/failed',
  ADD COLUMN `notify_time` datetime COMMENT '通联回调时间',
  ADD COLUMN `channel_type` varchar(50) COMMENT '通联返回渠道信息:支付渠道（如微信/支付宝/银联）',
  ADD COLUMN `bank_code` varchar(50) COMMENT '通联返回渠道信息:银行编码',
  ADD COLUMN `payer_account` varchar(100) COMMENT '通联返回渠道信息:付款方账号（如银行卡号/OpenID）';
-- 1. 支付机构标识（区分银联/通联，核心新增字段）
ALTER TABLE `publicbiz_order_payment`
    ADD COLUMN `payment_provider` varchar(20) DEFAULT NULL COMMENT '支付机构：UNIONPAY-银联商务/TONGLIAN-通联支付';

-- 2. 第三方子商户号（机构收款账户ID，银联/通联通用）
ALTER TABLE `publicbiz_order_payment`
    ADD COLUMN `third_party_sub_merchant_id` varchar(50) DEFAULT NULL COMMENT '第三方子商户号（银联/通联的机构收款账户ID）';

-- 3. 通联特有：支付方式细分编码（如微信JSAPI/支付宝H5）
ALTER TABLE `publicbiz_order_payment`
    ADD COLUMN `tpp_code` varchar(50) DEFAULT NULL COMMENT '通联支付编码（如WX_JSAPI/ALIPAY_H5）';

-- 4. 分账批次号（银联/通联分账流程通用）
ALTER TABLE `publicbiz_order_payment`
    ADD COLUMN `third_party_split_batch_no` varchar(50) DEFAULT NULL COMMENT '第三方分账批次号（银联split_batch_no/通联batchNo）';

-- 5. 分账金额与手续费（安心付分账流程）
ALTER TABLE `publicbiz_order_payment`
    ADD COLUMN `split_amount` decimal(12,2) DEFAULT NULL COMMENT '分账金额（支付金额-平台手续费）',
  ADD COLUMN `platform_fee` decimal(12,2) DEFAULT NULL COMMENT '平台手续费（元）';

-- 6. 分账状态与失败原因（分账流程跟踪）
ALTER TABLE `publicbiz_order_payment`
    ADD COLUMN `split_status` varchar(20) DEFAULT 'pending' COMMENT '分账状态：pending-待分账/success-分账成功/failed-分账失败',
  ADD COLUMN `split_fail_reason` varchar(255) DEFAULT NULL COMMENT '分账失败原因（如"账户冻结"）';

-- 7. 通联特有：分账方类型（平台/商户）
ALTER TABLE `publicbiz_order_payment`
    ADD COLUMN `split_party_type` varchar(20) DEFAULT NULL COMMENT '分账方类型（通联：PLATFORM-平台/MERCHANT-商户）';

-- 8. 退款相关扩展字段（银联/通联退款流程）
ALTER TABLE `publicbiz_order_payment`
    ADD COLUMN `refund_no` varchar(50) DEFAULT NULL COMMENT '平台退款单号',
  ADD COLUMN `refund_amount` decimal(12,2) DEFAULT NULL COMMENT '退款金额（元）',
  ADD COLUMN `refund_status` varchar(20) DEFAULT 'none' COMMENT '退款状态：none-未退款/pending-退款中/success-退款成功/failed-退款失败',
  ADD COLUMN `refund_time` datetime DEFAULT NULL COMMENT '退款完成时间',
  ADD COLUMN `third_party_refund_id` varchar(100) DEFAULT NULL COMMENT '第三方退款交易号（银联/通联退款ID）',
  ADD COLUMN `refund_reason` text COMMENT '退款原因';

-- 9. 回调原始内容（问题排查用，通用）
ALTER TABLE `publicbiz_order_payment`
    ADD COLUMN `notify_content` text COMMENT '第三方回调原始内容（JSON格式）';

-- 10. 新增索引（优化查询性能）
ALTER TABLE `publicbiz_order_payment`
    ADD KEY `idx_payment_provider` (`payment_provider`),  -- 按支付机构查询
  ADD KEY `idx_third_party_sub_merchant_id` (`third_party_sub_merchant_id`),  -- 按机构子商户号查询
  ADD KEY `idx_split_status` (`split_status`),  -- 分账状态查询
  ADD KEY `idx_refund_status` (`refund_status`);  -- 退款状态查询


-- 高校实践订单详情表
CREATE TABLE `publicbiz_practice_order` (
  -- 公共字段
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` BIGINT COMMENT '租户ID',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',

  -- 关联字段
  `order_id` BIGINT NOT NULL COMMENT '订单ID',
  `order_no` VARCHAR(50) NOT NULL COMMENT '订单号',

  -- 高校信息
  `university_name` VARCHAR(200) NOT NULL COMMENT '合作高校名称',
  `university_contact` VARCHAR(50) COMMENT '高校联系人',
  `university_phone` VARCHAR(20) COMMENT '高校联系电话',
  `university_email` VARCHAR(100) COMMENT '高校联系邮箱',

  -- 企业信息
  `enterprise_name` VARCHAR(200) NOT NULL COMMENT '合作企业名称',
  `enterprise_contact` VARCHAR(50) COMMENT '企业联系人',
  `enterprise_phone` VARCHAR(20) COMMENT '企业联系电话',
  `enterprise_email` VARCHAR(100) COMMENT '企业联系邮箱',

  -- 项目信息
  `project_name` VARCHAR(200) NOT NULL COMMENT '项目名称',
  `project_description` TEXT COMMENT '项目描述',
  `student_count` INT COMMENT '参与学生人数',
  `practice_duration` VARCHAR(50) COMMENT '实践时长',
  `practice_location` VARCHAR(500) COMMENT '实践地点',

  -- 费用信息
  `service_fee` DECIMAL(10,2) COMMENT '服务费',
  `management_fee` DECIMAL(10,2) COMMENT '管理费',
  `other_fee` DECIMAL(10,2) COMMENT '其他费用',

  -- 索引
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_id` (`order_id`),
  KEY `idx_order_no` (`order_no`),
  KEY `idx_university_name` (`university_name`),
  KEY `idx_enterprise_name` (`enterprise_name`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_deleted` (`deleted`)
) COMMENT='高校实践订单详情表';

-- 企业培训订单详情表
CREATE TABLE `publicbiz_training_order` (
  -- 公共字段
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` BIGINT COMMENT '租户ID',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',

  -- 关联字段
  `order_id` BIGINT NOT NULL COMMENT '订单ID',
  `order_no` VARCHAR(50) NOT NULL COMMENT '订单号',

  -- 企业信息
  `enterprise_name` VARCHAR(200) NOT NULL COMMENT '企业名称',
  `enterprise_contact` VARCHAR(50) COMMENT '企业联系人',
  `enterprise_phone` VARCHAR(20) COMMENT '企业联系电话',
  `enterprise_email` VARCHAR(100) COMMENT '企业联系邮箱',
  `enterprise_address` VARCHAR(500) COMMENT '企业地址',

  -- 培训信息
  `training_project` VARCHAR(200) NOT NULL COMMENT '培训项目名称',
  `training_description` TEXT COMMENT '培训项目描述',
  `participants_count` INT NOT NULL COMMENT '培训人数',
  `training_duration` VARCHAR(50) COMMENT '培训周期',
  `training_location` VARCHAR(500) COMMENT '培训地点',
  `training_type` VARCHAR(50) COMMENT '培训类型：技能培训/管理培训/认证培训',

  -- 费用信息
  `per_person_fee` DECIMAL(10,2) COMMENT '人均培训费',
  `total_fee` DECIMAL(10,2) COMMENT '总培训费',
  `material_fee` DECIMAL(10,2) COMMENT '教材费',
  `certification_fee` DECIMAL(10,2) COMMENT '认证费',

  -- 索引
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_id` (`order_id`),
  KEY `idx_order_no` (`order_no`),
  KEY `idx_enterprise_name` (`enterprise_name`),
  KEY `idx_training_project` (`training_project`),
  KEY `idx_training_type` (`training_type`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_deleted` (`deleted`)
) COMMENT='企业培训订单详情表';

-- 个人培训订单详情表
CREATE TABLE `publicbiz_personal_order` (
  -- 公共字段
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` BIGINT COMMENT '租户ID',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',

  -- 关联字段
  `order_id` BIGINT NOT NULL COMMENT '订单ID',
  `order_no` VARCHAR(50) NOT NULL COMMENT '订单号',

  -- 学员信息
  `student_oneid` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '学员OneID GUID',
  `student_name` VARCHAR(50) NOT NULL COMMENT '学员姓名',
  `student_phone` VARCHAR(20) COMMENT '学员电话',
  `student_email` VARCHAR(100) COMMENT '学员邮箱',
  `student_id_card` VARCHAR(18) COMMENT '学员身份证号',

  -- 培训信息
  `course_name` VARCHAR(200) NOT NULL COMMENT '课程名称',
  `course_type` VARCHAR(50) NOT NULL COMMENT '订单类型：个人培训/考试认证',
  `course_description` TEXT COMMENT '课程描述',
  `course_duration` VARCHAR(50) COMMENT '课程时长',
  `learning_status` VARCHAR(20) DEFAULT 'not_started' COMMENT '学习状态：not_started-未开始/learning-学习中/completed-已完成',
  `exam_status` VARCHAR(20) DEFAULT 'not_registered' COMMENT '考试状态：not_registered-未报名/registered-已报名/passed-已通过/failed-未通过',

  -- 费用信息
  `course_fee` DECIMAL(10,2) COMMENT '课程费',
  `exam_fee` DECIMAL(10,2) COMMENT '考试费',
  `certification_fee` DECIMAL(10,2) COMMENT '认证费',

  -- 索引
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_id` (`order_id`),
  KEY `idx_order_no` (`order_no`),
  KEY `idx_student_name` (`student_name`),
  KEY `idx_course_name` (`course_name`),
  KEY `idx_course_type` (`course_type`),
  KEY `idx_learning_status` (`learning_status`),
  KEY `idx_exam_status` (`exam_status`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_deleted` (`deleted`)
) COMMENT='个人培训订单详情表';

-- 家政服务订单详情表
CREATE TABLE `publicbiz_domestic_order` (
  -- 公共字段
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` BIGINT COMMENT '租户ID',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',

  -- 关联字段
  `order_id` BIGINT NOT NULL COMMENT '订单ID',
  `order_no` VARCHAR(50) NOT NULL COMMENT '订单号',

  -- 客户信息
  `customer_oneid` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '客户OneID GUID',
  `customer_name` VARCHAR(50) NOT NULL COMMENT '客户姓名',
  `customer_phone` VARCHAR(20) NOT NULL COMMENT '客户电话',
  `customer_address` VARCHAR(500) NOT NULL COMMENT '服务地址',
  `customer_remark` VARCHAR(2000) COMMENT '客户备注',

  -- 服务信息
  `service_category_id` bigint(20) NOT NULL COMMENT '服务分类ID',
  `service_category_name` varchar(255) NOT NULL COMMENT '服务分类名称',
  `service_package_id` BIGINT COMMENT '服务套餐ID',
  `service_package_name` VARCHAR(200) COMMENT '服务套餐名称',
  `service_start_date` DATE COMMENT '服务开始日期',
  `service_end_date` DATE COMMENT '服务结束日期',
  `service_duration` VARCHAR(50) COMMENT '服务时长',
  `service_frequency` VARCHAR(50) COMMENT '服务频次',
  `service_package_thumbnail` varchar(500) DEFAULT NULL COMMENT '套餐主图URL',
  `service_package_price` decimal(10,2) NOT NULL COMMENT '套餐价格',
  `service_package_original_price` decimal(10,2) DEFAULT NULL COMMENT '套餐原价',
  `service_package_unit` varchar(20) NOT NULL COMMENT '价格单位：次/项/天/月',
  `service_package_duration` varchar(100) DEFAULT NULL COMMENT '服务时长，如：4小时、26天、90天',
  `service_package_type` varchar(20) NOT NULL COMMENT '套餐类型：long-term-长周期套餐/count-card-次数次卡套餐',
  `service_description` text COMMENT '服务描述',
  `service_details` longtext COMMENT '详细服务内容，富文本格式',
  `service_process` longtext COMMENT '服务流程，富文本格式',
  `purchase_notice` text COMMENT '购买须知',
  `service_times` int(11) DEFAULT '1' COMMENT '服务次数',
  `unit_price` decimal(10,2) NOT NULL COMMENT '单价',
  `total_amount` decimal(10,2) NOT NULL COMMENT '订单总金额',
  `discount_amount` decimal(10,2) DEFAULT '0.00' COMMENT '优惠金额',
  `actual_amount` decimal(10,2) NOT NULL COMMENT '实付金额',
  `service_address` varchar(500) NOT NULL COMMENT '服务地址',
  `service_address_detail` varchar(200) DEFAULT NULL COMMENT '详细地址',
  `service_latitude` decimal(10,6) DEFAULT NULL COMMENT '服务地址纬度',
  `service_longitude` decimal(10,6) DEFAULT NULL COMMENT '服务地址经度',
  `service_schedule` json DEFAULT NULL COMMENT '服务时间安排(JSON格式)',

  -- 服务人员信息
  `practitioner_oneid` VARCHAR(36) COMMENT '服务人员OneID',
  `practitioner_name` VARCHAR(50) COMMENT '服务人员姓名',
  `practitioner_phone` VARCHAR(20) COMMENT '服务人员电话',
  `agency_id` BIGINT COMMENT '服务机构ID',
  `agency_name` VARCHAR(200) COMMENT '服务机构名称',

  -- 任务信息
  `task_count` INT DEFAULT 0 COMMENT '任务总数',
  `completed_task_count` INT DEFAULT 0 COMMENT '已完成任务数',
  `task_progress` DECIMAL(5,2) DEFAULT 0.00 COMMENT '任务进度百分比',

  -- 费用信息
  `service_fee` DECIMAL(10,2) COMMENT '服务费',
  `agency_fee` DECIMAL(10,2) COMMENT '机构费',
  `platform_fee` DECIMAL(10,2) COMMENT '平台费',

  -- 索引
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_id` (`order_id`),
  KEY `idx_order_no` (`order_no`),
  KEY `idx_customer_name` (`customer_name`),
  KEY `idx_practitioner_oneId` (`practitioner_oneId`),
  KEY `idx_agency_id` (`agency_id`),
  KEY `idx_service_start_date` (`service_start_date`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_deleted` (`deleted`)
) COMMENT='家政服务订单详情表';

-- 家政服务任务表
CREATE TABLE `publicbiz_domestic_task` (
  -- 公共字段
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` BIGINT COMMENT '租户ID',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',

  -- 关联字段
  `order_id` BIGINT NOT NULL COMMENT '订单ID',
  `order_no` VARCHAR(50) NOT NULL COMMENT '订单号',
  `domestic_order_id` BIGINT NOT NULL COMMENT '家政服务订单ID',

  -- 任务信息
  `task_no` VARCHAR(50) NOT NULL COMMENT '任务编号',
  `task_sequence` INT NOT NULL COMMENT '任务序号',
  `task_name` VARCHAR(200) NOT NULL COMMENT '任务名称',
  `task_description` TEXT COMMENT '任务描述',
  `task_type` VARCHAR(50) NOT NULL COMMENT '任务类型',
  `task_status` VARCHAR(20) NOT NULL DEFAULT 'pending' COMMENT '任务状态：pending-待分配/assigned-已分配/in_progress-进行中/completed-已完成/cancelled-已取消',

  -- 时间信息
  `planned_start_time` DATETIME COMMENT '计划开始时间',
  `planned_end_time` DATETIME COMMENT '计划结束时间',
  `actual_start_time` DATETIME COMMENT '实际开始时间',
  `actual_end_time` DATETIME COMMENT '实际结束时间',
  `duration` VARCHAR(50) COMMENT '任务时长',

  -- 服务人员信息
  `practitioner_oneid` VARCHAR(36) COMMENT '服务人员OneID',
  `practitioner_name` VARCHAR(50) COMMENT '服务人员姓名',
  `practitioner_phone` VARCHAR(20) COMMENT '服务人员电话',

  `schedule_date` date NOT NULL COMMENT '排班日期',
  `service_category_id` bigint(20) NOT NULL COMMENT '服务分类ID',
  `service_category_name` varchar(255) NOT NULL COMMENT '服务分类名称',
  `customer_id` bigint(20) NOT NULL COMMENT '客户ID',
  `customer_name` varchar(64) NOT NULL COMMENT '客户姓名',
  `customer_phone` varchar(20) NOT NULL COMMENT '客户电话',
  `service_address` varchar(255) NOT NULL COMMENT '服务地址',
  `punch_in_time` datetime DEFAULT NULL COMMENT '打卡开始时间',
  `punch_out_time` datetime DEFAULT NULL COMMENT '打卡结束时间',
  `punch_location` varchar(255) DEFAULT NULL COMMENT '打卡位置',
  `punch_latitude` decimal(10,7) DEFAULT NULL COMMENT '打卡纬度',
  `punch_longitude` decimal(10,7) DEFAULT NULL COMMENT '打卡经度',

  -- 索引
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_task_no` (`task_no`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_order_no` (`order_no`),
  KEY `idx_domestic_order_id` (`domestic_order_id`),
  KEY `idx_task_sequence` (`task_sequence`),
  KEY `idx_task_status` (`task_status`),
  KEY `idx_practitioner_oneId` (`practitioner_oneId`),
  KEY `idx_planned_start_time` (`planned_start_time`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_deleted` (`deleted`)
) COMMENT='家政服务任务表';

-- 服务打卡记录表 (publicbiz_aunt_punch_record)
CREATE TABLE `publicbiz_aunt_punch_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `tenant_id` bigint(20) DEFAULT '1' COMMENT '租户ID',
  `schedule_id` bigint(20) NOT NULL COMMENT '排班ID',
  `aunt_oneid` char(36) NOT NULL DEFAULT '' COMMENT '阿姨的OneID GUID',
  `aunt_name` varchar(64) NOT NULL COMMENT '阿姨姓名',
  `punch_type` tinyint(1) NOT NULL COMMENT '打卡类型(1-开始打卡,2-完成打卡)',
  `punch_time` datetime NOT NULL COMMENT '打卡时间',
  `punch_location` varchar(255) NOT NULL COMMENT '打卡位置',
  `punch_latitude` decimal(10,7) NOT NULL COMMENT '打卡纬度',
  `punch_longitude` decimal(10,7) NOT NULL COMMENT '打卡经度',
  `photo_count` int(2) NOT NULL DEFAULT '0' COMMENT '照片数量',
  `photo_urls` text DEFAULT NULL COMMENT '照片URL列表，用逗号分隔',
  `remark` varchar(500) DEFAULT NULL COMMENT '打卡备注',
  `creator` varchar(64) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='服务打卡记录表';

-- ==================== 结算中心相关表 ====================

-- 结算记录表
CREATE TABLE `publicbiz_settlement` (
  -- 公共字段
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` BIGINT COMMENT '租户ID',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',

  -- 结算基本信息
  `settlement_no` VARCHAR(50) NOT NULL COMMENT '结算单号',
  `settlement_type` VARCHAR(30) NOT NULL COMMENT '结算类型：order-订单结算/practitioner-阿姨结算/agency-机构结算/platform-平台结算',
  `settlement_period` VARCHAR(20) NOT NULL COMMENT '结算周期：daily-日结/weekly-周结/monthly-月结',
  `settlement_date` DATE NOT NULL COMMENT '结算日期',

  -- 关联信息
  `order_id` BIGINT COMMENT '关联订单ID',
  `order_no` VARCHAR(50) COMMENT '关联订单号',
  `practitioner_oneid` VARCHAR(36) COMMENT '关联阿姨OneID',
  `agency_id` BIGINT COMMENT '关联机构ID',

  -- 金额信息
  `total_amount` DECIMAL(12,2) NOT NULL COMMENT '结算总金额',
  `settlement_amount` DECIMAL(12,2) NOT NULL COMMENT '实际结算金额',
  `commission_amount` DECIMAL(12,2) NOT NULL DEFAULT 0.00 COMMENT '佣金金额',
  `tax_amount` DECIMAL(12,2) NOT NULL DEFAULT 0.00 COMMENT '税费金额',
  `other_deduction` DECIMAL(12,2) NOT NULL DEFAULT 0.00 COMMENT '其他扣减',

  -- 结算状态
  `settlement_status` VARCHAR(20) NOT NULL DEFAULT 'pending' COMMENT '结算状态：pending-待结算/processing-结算中/completed-已结算/failed-结算失败',
  `settlement_time` DATETIME COMMENT '结算时间',
  `settlement_method` VARCHAR(30) COMMENT '结算方式：bank_transfer-银行转账/alipay-支付宝/wechat-微信/other-其他',

  -- 操作信息
  `operator_id` BIGINT COMMENT '操作人ID',
  `operator_name` VARCHAR(50) COMMENT '操作人姓名',
  `remark` TEXT COMMENT '备注',

  -- 索引
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_settlement_no` (`settlement_no`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_settlement_type` (`settlement_type`),
  KEY `idx_settlement_period` (`settlement_period`),
  KEY `idx_settlement_date` (`settlement_date`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_practitioner_oneId` (`practitioner_oneId`),
  KEY `idx_agency_id` (`agency_id`),
  KEY `idx_settlement_status` (`settlement_status`),
  KEY `idx_operator_id` (`operator_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_deleted` (`deleted`)
) COMMENT='结算记录表';

-- 结算明细表
CREATE TABLE `publicbiz_settlement_detail` (
  -- 公共字段
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` BIGINT COMMENT '租户ID',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',

  -- 关联字段
  `settlement_id` BIGINT NOT NULL COMMENT '结算记录ID',
  `settlement_no` VARCHAR(50) NOT NULL COMMENT '结算单号',
  `order_id` BIGINT COMMENT '订单ID',
  `order_no` VARCHAR(50) COMMENT '订单号',

  -- 明细信息
  `item_type` VARCHAR(30) NOT NULL COMMENT '项目类型：service_fee-服务费/platform_fee-平台费/commission-佣金/tax-税费/deduction-扣减',
  `item_name` VARCHAR(200) NOT NULL COMMENT '项目名称',
  `item_description` TEXT COMMENT '项目描述',
  `amount` DECIMAL(12,2) NOT NULL COMMENT '金额',
  `rate` DECIMAL(5,2) COMMENT '费率百分比',
  `calculation_basis` VARCHAR(200) COMMENT '计算依据',

  -- 阿姨信息
  `practitioner_oneid` VARCHAR(36) NOT NULL COMMENT '阿姨OneID',
  `practitioner_name` VARCHAR(50) NOT NULL COMMENT '阿姨姓名',
  `practitioner_phone` VARCHAR(20) COMMENT '阿姨电话',
  `practitioner_bank_account` VARCHAR(50) COMMENT '阿姨银行账户',
  `practitioner_bank_name` VARCHAR(100) COMMENT '阿姨开户行',

    -- 机构信息
  `agency_id` BIGINT NOT NULL COMMENT '机构ID',
  `agency_name` VARCHAR(200) NOT NULL COMMENT '机构名称',
  `agency_contact` VARCHAR(50) COMMENT '机构联系人',
  `agency_phone` VARCHAR(20) COMMENT '机构联系电话',
  `agency_bank_account` VARCHAR(50) COMMENT '机构银行账户',
  `agency_bank_name` VARCHAR(100) COMMENT '机构开户行',

  -- 结算信息
  `settlement_period` VARCHAR(20) NOT NULL COMMENT '结算周期：weekly-周结/monthly-月结',
  `settlement_date` DATE NOT NULL COMMENT '结算日期',
  `order_count` INT NOT NULL DEFAULT 0 COMMENT '订单数量',
  `total_amount` DECIMAL(12,2) NOT NULL COMMENT '总金额',
  `practitioner_amount` DECIMAL(12,2) NOT NULL COMMENT '阿姨应得金额',
  `agency_amount` DECIMAL(12,2) NOT NULL COMMENT '机构应得金额',
  `platform_amount` DECIMAL(12,2) NOT NULL COMMENT '平台分成金额',
  `commission_rate` DECIMAL(5,2) NOT NULL COMMENT '佣金比例',

  -- 结算状态
  `settlement_status` VARCHAR(20) NOT NULL DEFAULT 'pending' COMMENT '结算状态：pending-待结算/processing-结算中/completed-已结算/failed-结算失败',
  `settlement_time` DATETIME COMMENT '结算时间',
  `settlement_method` VARCHAR(30) COMMENT '结算方式',
  `settlement_remark` TEXT COMMENT '结算备注',

  -- 索引
  PRIMARY KEY (`id`),
  KEY `idx_settlement_id` (`settlement_id`),
  KEY `idx_settlement_no` (`settlement_no`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_order_no` (`order_no`),
  KEY `idx_item_type` (`item_type`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_deleted` (`deleted`)
) COMMENT='结算明细表';

-- ==================== 对账单管理相关表 ====================

-- 对账单主表
CREATE TABLE `publicbiz_reconciliation_statement` (
  -- 公共字段
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` BIGINT COMMENT '租户ID',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',

  -- 对账单基本信息
  `statement_no` VARCHAR(50) NOT NULL COMMENT '对账单号，如：ZD20240816001',
  `statement_type` VARCHAR(30) NOT NULL COMMENT '对账单类型：agency-机构对账/platform-平台对账/practitioner-阿姨对账',
  `generation_time` DATETIME NOT NULL COMMENT '生成时间',

  -- 关联信息
  `agency_id` BIGINT COMMENT '关联机构ID',
  `agency_name` VARCHAR(200) COMMENT '机构名称',
  `practitioner_oneid` VARCHAR(36) COMMENT '关联阿姨OneID',
  `practitioner_name` VARCHAR(50) COMMENT '阿姨姓名',

  -- 金额信息
  `total_amount` DECIMAL(12,2) NOT NULL COMMENT '对账总金额',
  `agency_amount` DECIMAL(12,2) NOT NULL COMMENT '机构分成金额',
  `platform_amount` DECIMAL(12,2) NOT NULL COMMENT '平台分成金额',
  `agency_ratio` DECIMAL(5,2) NOT NULL COMMENT '机构分成比例(%)',
  `platform_ratio` DECIMAL(5,2) NOT NULL COMMENT '平台分成比例(%)',

  -- 订单信息
  `order_count` INT NOT NULL DEFAULT 0 COMMENT '包含订单数量',
  `order_list` JSON COMMENT '包含订单列表（JSON格式）',

  -- 对账状态
  `reconciliation_status` VARCHAR(20) NOT NULL DEFAULT 'pending' COMMENT '对账状态：pending-待对账确认/confirmed-已确认/paid-已支付/cancelled-已取消',
  `confirmation_time` DATETIME COMMENT '确认时间',
  `payment_time` DATETIME COMMENT '支付时间',
  `cancellation_time` DATETIME COMMENT '取消时间',
  `cancellation_reason` TEXT COMMENT '取消原因',

  -- 操作信息
  `operator_id` BIGINT COMMENT '操作人ID',
  `operator_name` VARCHAR(50) COMMENT '操作人姓名',
  `remark` TEXT COMMENT '备注',

  -- 索引
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_statement_no` (`statement_no`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_statement_type` (`statement_type`),
  KEY `idx_generation_time` (`generation_time`),
  KEY `idx_agency_id` (`agency_id`),
  KEY `idx_practitioner_oneid` (`practitioner_oneid`),
  KEY `idx_reconciliation_status` (`reconciliation_status`),
  KEY `idx_operator_id` (`operator_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_deleted` (`deleted`)
) COMMENT='对账单主表';

-- 对账单明细表
CREATE TABLE `publicbiz_reconciliation_detail` (
  -- 公共字段
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` BIGINT COMMENT '租户ID',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',

  -- 关联字段
  `statement_id` BIGINT NOT NULL COMMENT '对账单ID',
  `statement_no` VARCHAR(50) NOT NULL COMMENT '对账单号',
  `order_id` BIGINT NOT NULL COMMENT '订单ID',
  `order_no` VARCHAR(50) NOT NULL COMMENT '订单号',

  -- 订单信息
  `package_name` VARCHAR(200) NOT NULL COMMENT '套餐名称',
  `agency_name` VARCHAR(200) COMMENT '家政机构',
  `practitioner_name` VARCHAR(50) COMMENT '阿姨姓名',
  `order_time` DATETIME NOT NULL COMMENT '下单时间',
  `completion_time` DATETIME COMMENT '完成时间',
  `order_status` VARCHAR(20) NOT NULL COMMENT '订单状态：已完成/服务中',
  `settlement_status` VARCHAR(20) NOT NULL COMMENT '结算状态：待结算/结算中/已结算/结算失败',
  `order_amount` DECIMAL(10,2) NOT NULL COMMENT '订单金额',

  -- 分成信息
  `agency_amount` DECIMAL(10,2) NOT NULL COMMENT '机构分成金额',
  `platform_amount` DECIMAL(10,2) NOT NULL COMMENT '平台分成金额',
  `agency_ratio` DECIMAL(5,2) NOT NULL COMMENT '机构分成比例(%)',
  `platform_ratio` DECIMAL(5,2) NOT NULL COMMENT '平台分成比例(%)',

  -- 索引
  PRIMARY KEY (`id`),
  KEY `idx_statement_id` (`statement_id`),
  KEY `idx_statement_no` (`statement_no`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_order_no` (`order_no`),
  KEY `idx_agency_name` (`agency_name`),
  KEY `idx_practitioner_name` (`practitioner_name`),
  KEY `idx_order_status` (`order_status`),
  KEY `idx_settlement_status` (`settlement_status`),
  KEY `idx_order_time` (`order_time`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_deleted` (`deleted`)
) COMMENT='对账单明细表';

-- 发票管理表
CREATE TABLE `publicbiz_invoice` (
  -- 公共字段
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` BIGINT COMMENT '租户ID',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',

  -- 关联对账单信息
  `statement_id` BIGINT NOT NULL COMMENT '关联对账单ID',
  `statement_no` VARCHAR(50) NOT NULL COMMENT '对账单号',
  `agency_id` BIGINT NOT NULL COMMENT '机构ID',
  `agency_name` VARCHAR(200) NOT NULL COMMENT '机构名称',
  `reconciliation_amount` DECIMAL(12,2) NOT NULL COMMENT '对账金额',

  -- 发票信息
  `invoice_no` VARCHAR(50) COMMENT '发票号码',
  `invoice_date` DATE COMMENT '开票日期',
  `invoice_type` VARCHAR(30) COMMENT '发票类型：增值税普通发票/增值税专用发票/电子发票',
  `invoice_amount` DECIMAL(12,2) COMMENT '发票金额',
  `tax_rate` DECIMAL(5,2) DEFAULT 6.00 COMMENT '税率(%)',
  `tax_amount` DECIMAL(12,2) COMMENT '税额',
  `invoice_status` VARCHAR(20) NOT NULL DEFAULT 'not_invoiced' COMMENT '开票状态：not_invoiced-未开票/invoiced-已开票/partially_invoiced-部分开票',

  -- 开票信息维护
  `invoicing_status` VARCHAR(20) NOT NULL DEFAULT 'not_invoiced' COMMENT '开票状态：not_invoiced-未开票/invoicing-开票中/invoiced-已开票/failed-开票失败',
  `invoicing_remark` TEXT COMMENT '开票备注',

  -- 操作信息
  `operator_id` BIGINT COMMENT '操作人ID',
  `operator_name` VARCHAR(50) COMMENT '操作人姓名',
  `remark` TEXT COMMENT '备注',

  -- 索引
  PRIMARY KEY (`id`),
  KEY `idx_statement_id` (`statement_id`),
  KEY `idx_statement_no` (`statement_no`),
  KEY `idx_agency_id` (`agency_id`),
  KEY `idx_invoice_no` (`invoice_no`),
  KEY `idx_invoice_date` (`invoice_date`),
  KEY `idx_invoice_status` (`invoice_status`),
  KEY `idx_invoicing_status` (`invoicing_status`),
  KEY `idx_operator_id` (`operator_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_deleted` (`deleted`)
) COMMENT='发票管理表';

-- 订单资金列表可以通过以下现有表合成：
-- 1. publicbiz_domestic_order - 订单基本信息
-- 2. publicbiz_settlement - 结算信息
-- 3. publicbiz_reconciliation_statement + publicbiz_reconciliation_detail - 对账信息
-- 4. publicbiz_invoice - 发票信息
--
-- 合成查询示例：
-- SELECT
--     o.order_id, o.order_no, o.service_package_name as package_name,
--     o.agency_name, o.practitioner_name, o.total_amount,
--     s.settlement_status, s.settlement_time, s.settlement_method,
--     rd.reconciliation_status, rs.statement_no,
--     i.invoice_status, i.invoice_no, i.invoice_date
-- FROM publicbiz_domestic_order o
-- LEFT JOIN publicbiz_settlement s ON o.order_id = s.order_id
-- LEFT JOIN publicbiz_reconciliation_detail rd ON o.order_id = rd.order_id
-- LEFT JOIN publicbiz_reconciliation_statement rs ON rd.statement_id = rs.id
-- LEFT JOIN publicbiz_invoice i ON rs.id = i.statement_id
-- WHERE o.deleted = 0;

-- 待结算订单可以通过以下查询从订单主表获取：
-- SELECT
--     o.id as order_id, o.order_no, o.total_amount,
--     do.service_package_name as package_name, do.agency_name, do.practitioner_name,
--     o.create_time as order_time, do.service_end_date as completion_time,
--     o.order_status, o.settlement_status, o.is_selected_for_reconciliation,
--     o.selection_time, o.selector_id, o.selector_name
-- FROM publicbiz_order o
-- LEFT JOIN publicbiz_domestic_order do ON o.id = do.order_id
-- WHERE o.order_status = 'completed'
-- AND o.payment_status = 'paid'
-- AND o.settlement_status = 'pending'
-- AND o.deleted = 0;
