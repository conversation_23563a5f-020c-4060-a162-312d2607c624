package cn.bztmaster.cnt.framework.security.core.util;

import cn.bztmaster.cnt.framework.security.core.LoginUser;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.AfterEach;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * SecurityFrameworkUtils 测试类
 */
public class SecurityFrameworkUtilsTest {

    @BeforeEach
    void setUp() {
        // 清理安全上下文
        SecurityContextHolder.clearContext();
    }

    @AfterEach
    void tearDown() {
        // 清理安全上下文
        SecurityContextHolder.clearContext();
    }

    @Test
    void testGetLoginUserAgencyId_正常情况() {
        // 准备测试数据
        Map<String, String> userInfo = new HashMap<>();
        userInfo.put(LoginUser.INFO_KEY_AGENCY_ID, "123");
        userInfo.put(LoginUser.INFO_KEY_NICKNAME, "测试用户");

        LoginUser loginUser = new LoginUser();
        loginUser.setId(1L);
        loginUser.setInfo(userInfo);

        // 设置到安全上下文
        UsernamePasswordAuthenticationToken authentication = 
            new UsernamePasswordAuthenticationToken(loginUser, null, null);
        SecurityContextHolder.getContext().setAuthentication(authentication);

        // 执行测试
        Long agencyId = SecurityFrameworkUtils.getLoginUserAgencyId();

        // 验证结果
        assertNotNull(agencyId);
        assertEquals(123L, agencyId);
    }

    @Test
    void testGetLoginUserAgencyId_机构ID为空() {
        // 准备测试数据
        Map<String, String> userInfo = new HashMap<>();
        userInfo.put(LoginUser.INFO_KEY_NICKNAME, "测试用户");
        // 不设置 AGENCY_ID

        LoginUser loginUser = new LoginUser();
        loginUser.setId(1L);
        loginUser.setInfo(userInfo);

        // 设置到安全上下文
        UsernamePasswordAuthenticationToken authentication = 
            new UsernamePasswordAuthenticationToken(loginUser, null, null);
        SecurityContextHolder.getContext().setAuthentication(authentication);

        // 执行测试
        Long agencyId = SecurityFrameworkUtils.getLoginUserAgencyId();

        // 验证结果
        assertNull(agencyId);
    }

    @Test
    void testGetLoginUserAgencyId_用户未登录() {
        // 不设置任何用户到安全上下文

        // 执行测试
        Long agencyId = SecurityFrameworkUtils.getLoginUserAgencyId();

        // 验证结果
        assertNull(agencyId);
    }

    @Test
    void testGetLoginUserAgencyId_用户信息为空() {
        // 准备测试数据
        LoginUser loginUser = new LoginUser();
        loginUser.setId(1L);
        loginUser.setInfo(null); // 用户信息为空

        // 设置到安全上下文
        UsernamePasswordAuthenticationToken authentication = 
            new UsernamePasswordAuthenticationToken(loginUser, null, null);
        SecurityContextHolder.getContext().setAuthentication(authentication);

        // 执行测试
        Long agencyId = SecurityFrameworkUtils.getLoginUserAgencyId();

        // 验证结果
        assertNull(agencyId);
    }

    @Test
    void testGetLoginUserAgencyId_与合作伙伴ID对比() {
        // 准备测试数据
        Map<String, String> userInfo = new HashMap<>();
        userInfo.put(LoginUser.INFO_KEY_AGENCY_ID, "456");
        userInfo.put(LoginUser.INFO_KEY_PARTNER_ID, "789");
        userInfo.put(LoginUser.INFO_KEY_NICKNAME, "测试用户");

        LoginUser loginUser = new LoginUser();
        loginUser.setId(1L);
        loginUser.setInfo(userInfo);

        // 设置到安全上下文
        UsernamePasswordAuthenticationToken authentication = 
            new UsernamePasswordAuthenticationToken(loginUser, null, null);
        SecurityContextHolder.getContext().setAuthentication(authentication);

        // 执行测试
        Long agencyId = SecurityFrameworkUtils.getLoginUserAgencyId();
        Long partnerId = SecurityFrameworkUtils.getLoginUserPartnerId();

        // 验证结果
        assertNotNull(agencyId);
        assertNotNull(partnerId);
        assertEquals(456L, agencyId);
        assertEquals(789L, partnerId);
        assertNotEquals(agencyId, partnerId); // 确保两个ID不同
    }

    @Test
    void testGetLoginUserAgencyId_常量定义正确() {
        // 验证常量定义
        assertEquals("agencyId", LoginUser.INFO_KEY_AGENCY_ID);
        assertEquals("partnerId", LoginUser.INFO_KEY_PARTNER_ID);
        assertEquals("nickname", LoginUser.INFO_KEY_NICKNAME);
        assertEquals("deptId", LoginUser.INFO_KEY_DEPT_ID);
        assertEquals("accountType", LoginUser.INFO_KEY_ACCOUNT_TYPE);
    }
}
