# SecurityFrameworkUtils 新增 getLoginUserAgencyId() 方法说明

## 修改概述

参考 `getLoginUserPartnerId()` 方法的实现模式，在 SecurityFrameworkUtils 类中新增了 `getLoginUserAgencyId()` 方法，用于获取当前登录用户的关联机构ID。

## 实现内容

### 1. 新增方法

在 `SecurityFrameworkUtils` 类中添加了新方法：

```java
/**
 * 获得当前用户的关联机构ID
 *
 * @return 机构ID，可能为null
 */
@Nullable
public static Long getLoginUserAgencyId() {
    LoginUser loginUser = getLoginUser();
    return loginUser != null ? MapUtil.getLong(loginUser.getInfo(), LoginUser.INFO_KEY_AGENCY_ID) : null;
}
```

### 2. 新增常量

在 `LoginUser` 类中添加了新的常量定义：

```java
public static final String INFO_KEY_AGENCY_ID = "agencyId";
```

### 3. 数据模型扩展

在 `AdminUserDO` 类中添加了机构相关字段：

```java
/**
 * 所属机构ID
 */
private Long agencyId;
/**
 * 所属机构名称
 */
private String agencyName;
```

### 4. 用户信息构建

在 `OAuth2TokenServiceImpl.buildUserInfo()` 方法中添加了机构ID的设置：

```java
return MapUtil.builder(LoginUser.INFO_KEY_NICKNAME, user.getNickname())
        .put(LoginUser.INFO_KEY_DEPT_ID, StrUtil.toStringOrNull(user.getDeptId()))
        .put(LoginUser.INFO_KEY_ACCOUNT_TYPE, StrUtil.toStringOrNull(user.getAccountType()))
        .put(LoginUser.INFO_KEY_PARTNER_ID, StrUtil.toStringOrNull(user.getPartnerId()))
        .put(LoginUser.INFO_KEY_AGENCY_ID, StrUtil.toStringOrNull(user.getAgencyId()))
        .build();
```

## 方法特性

### 返回值
- **类型**：`Long`
- **可空性**：可能为 `null`（使用 `@Nullable` 注解标记）
- **来源**：从当前登录用户的 `info` 信息中获取

### 实现模式
- **一致性**：与 `getLoginUserPartnerId()` 方法保持完全一致的实现模式
- **安全性**：包含完整的空值检查，避免 NPE
- **复用性**：复用现有的 `getLoginUser()` 方法

### 使用场景
1. **权限控制**：根据用户所属机构进行数据权限过滤
2. **业务逻辑**：在业务处理中获取当前用户的机构信息
3. **日志记录**：在操作日志中记录用户所属机构
4. **数据关联**：在创建数据时自动关联用户所属机构

## 使用示例

```java
// 获取当前用户的机构ID
Long agencyId = SecurityFrameworkUtils.getLoginUserAgencyId();

if (agencyId != null) {
    // 根据机构ID进行业务处理
    processBusinessByAgency(agencyId);
} else {
    // 用户未关联机构的处理逻辑
    handleNoAgencyUser();
}
```

## 与现有方法的对比

| 方法名 | 返回类型 | 获取字段 | 用途 |
|--------|----------|----------|------|
| `getLoginUserId()` | `Long` | 用户ID | 获取当前用户编号 |
| `getLoginUserNickname()` | `String` | 用户昵称 | 获取当前用户昵称 |
| `getLoginUserDeptId()` | `Long` | 部门ID | 获取当前用户部门编号 |
| `getLoginUserPartnerId()` | `Long` | 合作伙伴ID | 获取当前用户合作伙伴ID |
| `getLoginUserAgencyId()` | `Long` | **机构ID** | **获取当前用户机构ID** |

## 测试验证

提供了完整的单元测试，覆盖以下场景：
1. 正常获取机构ID
2. 机构ID为空的情况
3. 用户未登录的情况
4. 用户信息为空的情况
5. 与合作伙伴ID的对比验证
6. 常量定义的正确性验证

## 注意事项

1. **数据库字段**：需要确保 `system_users` 表中已添加 `agency_id` 和 `agency_name` 字段
2. **数据同步**：现有用户的机构信息需要通过数据迁移或业务逻辑进行设置
3. **权限设计**：建议在权限设计中考虑机构级别的数据隔离
4. **缓存一致性**：如果使用了用户信息缓存，需要在机构信息变更时及时更新缓存

## 相关文件

- `SecurityFrameworkUtils.java` - 主要实现文件
- `LoginUser.java` - 常量定义
- `AdminUserDO.java` - 数据模型扩展
- `OAuth2TokenServiceImpl.java` - 用户信息构建
- `SecurityFrameworkUtilsTest.java` - 单元测试

## 版本兼容性

- **向后兼容**：新增方法不影响现有功能
- **扩展性**：为后续机构相关功能提供基础支持
- **一致性**：与现有代码风格和架构保持一致
