# UserSaveReqVO 新增机构字段说明

## 修改概述

在 UserSaveReqVO 类中参考现有的所属合作伙伴字段（partnerId 和 partnerName）的定义模式，添加了关联机构ID（agencyId）和关联机构名称（agencyName）两个新字段。

## 新增字段定义

### 1. agencyId（关联机构ID）

```java
@Schema(description = "关联机构ID", example = "2001")
@DiffLogField(name = "关联机构ID")
private Long agencyId;
```

**字段特性：**
- **字段类型**：`Long`
- **Schema 描述**："关联机构ID"
- **示例值**："2001"
- **操作日志**：使用 `@DiffLogField(name = "关联机构ID")` 注解记录字段变更

### 2. agencyName（关联机构名称）

```java
@Schema(description = "关联机构名称", example = "XX就业服务机构")
@Size(max = 100, message = "关联机构名称长度不能超过100个字符")
@DiffLogField(name = "关联机构名称")
private String agencyName;
```

**字段特性：**
- **字段类型**：`String`
- **Schema 描述**："关联机构名称"
- **示例值**："XX就业服务机构"
- **长度验证**：使用 `@Size(max = 100)` 限制最大长度为100个字符
- **操作日志**：使用 `@DiffLogField(name = "关联机构名称")` 注解记录字段变更

## 字段定义对比

| 特性 | 合作伙伴字段 | 机构字段 |
|------|-------------|----------|
| **ID字段** | `partnerId (Long)` | `agencyId (Long)` |
| **名称字段** | `partnerName (String)` | `agencyName (String)` |
| **Schema描述** | "所属合作伙伴ID/名称" | "关联机构ID/名称" |
| **示例值** | "1001" / "XX合作伙伴" | "2001" / "XX就业服务机构" |
| **长度验证** | `@Size(max = 100)` | `@Size(max = 100)` |
| **操作日志** | `@DiffLogField` | `@DiffLogField` |

## 字段位置和顺序

新增的机构字段按照以下顺序放置在类中：

```java
// 合作伙伴字段
@Schema(description = "所属合作伙伴ID", example = "1001")
@DiffLogField(name = "所属合作伙伴ID")
private Long partnerId;

@Schema(description = "所属合作伙伴名称", example = "XX合作伙伴")
@Size(max = 100, message = "所属合作伙伴名称长度不能超过100个字符")
@DiffLogField(name = "所属合作伙伴名称")
private String partnerName;

// 机构字段（新增）
@Schema(description = "关联机构ID", example = "2001")
@DiffLogField(name = "关联机构ID")
private Long agencyId;

@Schema(description = "关联机构名称", example = "XX就业服务机构")
@Size(max = 100, message = "关联机构名称长度不能超过100个字符")
@DiffLogField(name = "关联机构名称")
private String agencyName;
```

**字段顺序逻辑：**
1. 合作伙伴ID → 合作伙伴名称
2. 机构ID → 机构名称
3. 机构字段紧跟在合作伙伴字段之后，保持逻辑关联性

## 注解使用说明

### 1. @Schema 注解

用于 Swagger API 文档生成：

```java
@Schema(description = "字段描述", example = "示例值")
```

- **description**：字段的中文描述，用于API文档展示
- **example**：字段的示例值，帮助开发者理解字段用途

### 2. @Size 注解

用于字符串长度验证：

```java
@Size(max = 100, message = "关联机构名称长度不能超过100个字符")
```

- **max**：最大长度限制
- **message**：验证失败时的错误消息

### 3. @DiffLogField 注解

用于操作日志记录：

```java
@DiffLogField(name = "关联机构ID")
```

- **name**：在操作日志中显示的字段名称
- 当字段值发生变化时，会自动记录到操作日志中

## 数据流向和使用场景

### 1. 前端到后端

```
前端表单 → UserSaveReqVO → AdminUserServiceImpl → AdminUserDO → 数据库
```

- 前端可以选择性传递 `agencyId` 和 `agencyName`
- 后端会根据 `partnerId` 自动同步机构信息（如果前端未传递）

### 2. 后端到前端

```
数据库 → AdminUserDO → UserSaveReqVO → 前端表单
```

- 用户编辑时，机构信息会回显到前端表单
- 支持用户手动修改机构信息

### 3. 操作日志记录

```
字段变更 → @DiffLogField → 操作日志系统 → 日志存储
```

- 机构ID和名称的变更会自动记录到操作日志
- 便于追踪用户机构关联关系的变化历史

## 验证规则

### 1. 字段验证

- **agencyId**：无特殊验证，允许为空
- **agencyName**：最大长度100个字符，允许为空

### 2. 业务验证

- 机构信息的一致性由 `AdminUserServiceImpl` 中的同步逻辑保证
- 当 `partnerId` 变化时，会自动更新对应的机构信息

## 测试验证

提供了完整的单元测试，覆盖以下场景：

1. **字段验证测试**：
   - 正常情况下的字段验证
   - 超长字符串的验证错误
   - 空值和null值的处理

2. **字段存在性测试**：
   - 验证字段能正确设置和获取值
   - 验证字段类型与合作伙伴字段一致

3. **注解验证测试**：
   - 验证 `@Schema` 注解的正确配置
   - 验证 `@DiffLogField` 注解的正确配置
   - 验证 `@Size` 注解的验证逻辑

4. **字段顺序测试**：
   - 验证机构字段在合作伙伴字段之后
   - 验证字段顺序的逻辑性

## 使用示例

### 1. 创建用户时设置机构信息

```java
UserSaveReqVO reqVO = new UserSaveReqVO();
reqVO.setUsername("testuser");
reqVO.setNickname("测试用户");
reqVO.setPartnerId(1001L);
reqVO.setPartnerName("XX合作伙伴");
reqVO.setAgencyId(2001L);  // 可选，后端会自动同步
reqVO.setAgencyName("XX就业服务机构");  // 可选，后端会自动同步
```

### 2. 更新用户时修改机构信息

```java
UserSaveReqVO reqVO = new UserSaveReqVO();
reqVO.setId(1L);
reqVO.setPartnerId(1002L);  // 变更合作伙伴
// agencyId 和 agencyName 会根据新的 partnerId 自动同步
```

## 注意事项

1. **数据一致性**：机构信息主要通过后端同步逻辑维护，前端传递的值可能会被覆盖
2. **字段关联性**：agencyId 和 agencyName 与 partnerId 存在关联关系
3. **验证优先级**：字段级验证优先于业务级验证
4. **日志记录**：字段变更会自动记录到操作日志，便于审计

## 相关文件

- `UserSaveReqVO.java` - 主要修改文件
- `AdminUserServiceImpl.java` - 机构信息同步逻辑
- `AdminUserDO.java` - 用户数据模型
- `UserSaveReqVOTest.java` - 单元测试文件

## 版本兼容性

- **向后兼容**：新增字段不影响现有功能
- **前端兼容**：前端可以选择性使用新字段
- **API兼容**：API接口保持向后兼容，新字段为可选字段
