package cn.bztmaster.cnt.module.system.controller.admin.user.vo.user;

import org.junit.jupiter.api.Test;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

/**
 * UserSaveReqVO 字段验证测试
 */
public class UserSaveReqVOTest {

    private final ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
    private final Validator validator = factory.getValidator();

    @Test
    void testAgencyNameValidation_正常情况() {
        // 准备测试数据
        UserSaveReqVO reqVO = new UserSaveReqVO();
        reqVO.setId(1L); // 设置ID，避免密码验证
        reqVO.setUsername("testuser");
        reqVO.setNickname("测试用户");
        reqVO.setAccountType(1);
        reqVO.setAgencyName("测试就业服务机构");

        // 执行验证
        Set<ConstraintViolation<UserSaveReqVO>> violations = validator.validate(reqVO);

        // 验证结果 - 机构名称字段应该通过验证
        assertTrue(violations.stream().noneMatch(v -> v.getPropertyPath().toString().equals("agencyName")));
    }

    @Test
    void testAgencyNameValidation_超长字符串() {
        // 准备测试数据
        UserSaveReqVO reqVO = new UserSaveReqVO();
        reqVO.setId(1L); // 设置ID，避免密码验证
        reqVO.setUsername("testuser");
        reqVO.setNickname("测试用户");
        reqVO.setAccountType(1);
        // 创建超过100个字符的机构名称
        reqVO.setAgencyName("a".repeat(101));

        // 执行验证
        Set<ConstraintViolation<UserSaveReqVO>> violations = validator.validate(reqVO);

        // 验证结果 - 应该有机构名称长度验证错误
        assertTrue(violations.stream().anyMatch(v -> 
            v.getPropertyPath().toString().equals("agencyName") && 
            v.getMessage().contains("关联机构名称长度不能超过100个字符")
        ));
    }

    @Test
    void testAgencyNameValidation_空字符串() {
        // 准备测试数据
        UserSaveReqVO reqVO = new UserSaveReqVO();
        reqVO.setId(1L); // 设置ID，避免密码验证
        reqVO.setUsername("testuser");
        reqVO.setNickname("测试用户");
        reqVO.setAccountType(1);
        reqVO.setAgencyName(""); // 空字符串

        // 执行验证
        Set<ConstraintViolation<UserSaveReqVO>> violations = validator.validate(reqVO);

        // 验证结果 - 空字符串应该通过验证（因为没有@NotBlank注解）
        assertTrue(violations.stream().noneMatch(v -> v.getPropertyPath().toString().equals("agencyName")));
    }

    @Test
    void testAgencyNameValidation_null值() {
        // 准备测试数据
        UserSaveReqVO reqVO = new UserSaveReqVO();
        reqVO.setId(1L); // 设置ID，避免密码验证
        reqVO.setUsername("testuser");
        reqVO.setNickname("测试用户");
        reqVO.setAccountType(1);
        reqVO.setAgencyName(null); // null值

        // 执行验证
        Set<ConstraintViolation<UserSaveReqVO>> violations = validator.validate(reqVO);

        // 验证结果 - null值应该通过验证（因为没有@NotNull注解）
        assertTrue(violations.stream().noneMatch(v -> v.getPropertyPath().toString().equals("agencyName")));
    }

    @Test
    void testAgencyIdField_存在性验证() {
        // 准备测试数据
        UserSaveReqVO reqVO = new UserSaveReqVO();
        reqVO.setAgencyId(2001L);

        // 验证字段存在性
        assertNotNull(reqVO.getAgencyId());
        assertEquals(2001L, reqVO.getAgencyId());
    }

    @Test
    void testAgencyNameField_存在性验证() {
        // 准备测试数据
        UserSaveReqVO reqVO = new UserSaveReqVO();
        reqVO.setAgencyName("XX就业服务机构");

        // 验证字段存在性
        assertNotNull(reqVO.getAgencyName());
        assertEquals("XX就业服务机构", reqVO.getAgencyName());
    }

    @Test
    void testFieldOrder_机构字段在合作伙伴字段之后() {
        // 通过反射验证字段顺序
        java.lang.reflect.Field[] fields = UserSaveReqVO.class.getDeclaredFields();
        
        int partnerNameIndex = -1;
        int agencyIdIndex = -1;
        int agencyNameIndex = -1;
        
        for (int i = 0; i < fields.length; i++) {
            String fieldName = fields[i].getName();
            if ("partnerName".equals(fieldName)) {
                partnerNameIndex = i;
            } else if ("agencyId".equals(fieldName)) {
                agencyIdIndex = i;
            } else if ("agencyName".equals(fieldName)) {
                agencyNameIndex = i;
            }
        }
        
        // 验证字段顺序：partnerName < agencyId < agencyName
        assertTrue(partnerNameIndex < agencyIdIndex, "agencyId 应该在 partnerName 之后");
        assertTrue(agencyIdIndex < agencyNameIndex, "agencyName 应该在 agencyId 之后");
    }

    @Test
    void testAgencyFields_与合作伙伴字段对比() {
        // 验证机构字段与合作伙伴字段的定义一致性
        UserSaveReqVO reqVO = new UserSaveReqVO();
        
        // 设置合作伙伴字段
        reqVO.setPartnerId(1001L);
        reqVO.setPartnerName("XX合作伙伴");
        
        // 设置机构字段
        reqVO.setAgencyId(2001L);
        reqVO.setAgencyName("XX就业服务机构");
        
        // 验证字段类型一致性
        assertEquals(reqVO.getPartnerId().getClass(), reqVO.getAgencyId().getClass());
        assertEquals(reqVO.getPartnerName().getClass(), reqVO.getAgencyName().getClass());
        
        // 验证字段值设置正确
        assertEquals(1001L, reqVO.getPartnerId());
        assertEquals("XX合作伙伴", reqVO.getPartnerName());
        assertEquals(2001L, reqVO.getAgencyId());
        assertEquals("XX就业服务机构", reqVO.getAgencyName());
    }

    @Test
    void testDiffLogFieldAnnotation_存在性验证() throws NoSuchFieldException {
        // 验证 agencyId 字段的 @DiffLogField 注解
        java.lang.reflect.Field agencyIdField = UserSaveReqVO.class.getDeclaredField("agencyId");
        com.mzt.logapi.starter.annotation.DiffLogField agencyIdAnnotation = 
            agencyIdField.getAnnotation(com.mzt.logapi.starter.annotation.DiffLogField.class);
        assertNotNull(agencyIdAnnotation);
        assertEquals("关联机构ID", agencyIdAnnotation.name());
        
        // 验证 agencyName 字段的 @DiffLogField 注解
        java.lang.reflect.Field agencyNameField = UserSaveReqVO.class.getDeclaredField("agencyName");
        com.mzt.logapi.starter.annotation.DiffLogField agencyNameAnnotation = 
            agencyNameField.getAnnotation(com.mzt.logapi.starter.annotation.DiffLogField.class);
        assertNotNull(agencyNameAnnotation);
        assertEquals("关联机构名称", agencyNameAnnotation.name());
    }

    @Test
    void testSchemaAnnotation_存在性验证() throws NoSuchFieldException {
        // 验证 agencyId 字段的 @Schema 注解
        java.lang.reflect.Field agencyIdField = UserSaveReqVO.class.getDeclaredField("agencyId");
        io.swagger.v3.oas.annotations.media.Schema agencyIdSchema = 
            agencyIdField.getAnnotation(io.swagger.v3.oas.annotations.media.Schema.class);
        assertNotNull(agencyIdSchema);
        assertEquals("关联机构ID", agencyIdSchema.description());
        assertEquals("2001", agencyIdSchema.example());
        
        // 验证 agencyName 字段的 @Schema 注解
        java.lang.reflect.Field agencyNameField = UserSaveReqVO.class.getDeclaredField("agencyName");
        io.swagger.v3.oas.annotations.media.Schema agencyNameSchema = 
            agencyNameField.getAnnotation(io.swagger.v3.oas.annotations.media.Schema.class);
        assertNotNull(agencyNameSchema);
        assertEquals("关联机构名称", agencyNameSchema.description());
        assertEquals("XX就业服务机构", agencyNameSchema.example());
    }
}
